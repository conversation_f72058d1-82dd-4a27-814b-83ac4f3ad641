version: '3.8'

services:
  # MCP Gateway - 统一入口
  mcp-gateway:
    build:
      context: .
      dockerfile: docker/mcp-gateway/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - MCP_REGISTRY_URL=http://mcp-registry:3000
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - mcp-registry
    networks:
      - mcp-network

  # MCP Registry - 服务注册中心
  mcp-registry:
    image: consul:1.15
    ports:
      - "8500:8500"
    command: agent -server -bootstrap -ui -client=0.0.0.0
    networks:
      - mcp-network

  # Redis - 缓存和消息队列
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - mcp-network

  # Yahoo Finance MCP (stdio -> HTTP)
  yahoo-finance-mcp:
    build:
      context: ./scripts/mcp/yahoo-finance-mcp
      dockerfile: Dockerfile
    environment:
      - MCP_TRANSPORT=http
      - MCP_PORT=3001
      - PYTHONPATH=/app/src
    ports:
      - "3001:3001"
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Cauldron MCP (你的金融工具)
  cauldron-mcp:
    build:
      context: .
      dockerfile: docker/cauldron-mcp/Dockerfile
    environment:
      - MCP_TRANSPORT=http
      - MCP_PORT=3002
      - CAULDRON_API_URL=https://cauldron.herokuapp.com
      - CAULDRON_API_KEY=${CAULDRON_API_KEY}
    ports:
      - "3002:3002"
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Tusita Palace MCP (N8N工作流)
  tusita-palace-mcp:
    build:
      context: .
      dockerfile: docker/tusita-palace-mcp/Dockerfile
    environment:
      - MCP_TRANSPORT=http
      - MCP_PORT=3003
      - N8N_WEBHOOK_URL=${N8N_WEBHOOK_URL}
      - N8N_API_KEY=${N8N_API_KEY}
    ports:
      - "3003:3003"
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MCP Load Balancer
  mcp-lb:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./config/nginx/mcp-nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - yahoo-finance-mcp
      - cauldron-mcp
      - tusita-palace-mcp
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge

volumes:
  redis-data:
  consul-data: