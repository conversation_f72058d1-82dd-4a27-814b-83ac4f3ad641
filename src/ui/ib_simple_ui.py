#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版IB基本面数据UI组件
不依赖数据库，直接使用IB API

功能：
- IB连接测试
- 股票基本面数据查询
- 数据展示和分析

作者：太公心易BI系统
版本：v1.0
"""

import streamlit as st
import pandas as pd
import asyncio
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 尝试导入IB相关模块
try:
    import asyncio
    # 设置事件循环策略以避免Streamlit中的问题
    try:
        asyncio.get_event_loop()
    except RuntimeError:
        asyncio.set_event_loop(asyncio.new_event_loop())

    from ib_insync import IB, Stock, util
    IB_AVAILABLE = True
except ImportError:
    IB_AVAILABLE = False
except Exception as e:
    IB_AVAILABLE = False
    print(f"IB模块初始化失败: {e}")


class IBSimpleUI:
    """简化版IB基本面数据UI"""
    
    def __init__(self):
        self.host = os.getenv("IB_HOST", "127.0.0.1")
        self.port = int(os.getenv("IB_PORT", 4002))
        self.client_id = int(os.getenv("IB_CLIENT_ID", 1))
    
    def render(self):
        """渲染UI界面"""
        st.markdown("### 💎 六壬察心 - IB基本面数据")
        st.markdown("*降魔杵专属功能 - 深度洞察市场情绪面*")
        
        if not IB_AVAILABLE:
            st.error("❌ IB模块不可用")
            st.info("💡 请安装: `pip install ib-insync`")
            return
        
        # 连接状态
        st.markdown("#### 🔌 IB连接配置")
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("主机", self.host)
        with col2:
            st.metric("端口", self.port)
        with col3:
            st.metric("客户端ID", self.client_id)
        
        # 连接测试
        if st.button("🔍 测试IB连接", type="primary"):
            self._test_connection()
        
        st.markdown("---")
        
        # 股票数据查询
        st.markdown("#### 📊 股票数据查询")
        
        col1, col2 = st.columns([3, 1])
        
        with col1:
            symbols_input = st.text_input(
                "股票代码",
                value="AAPL,MSFT,GOOGL",
                placeholder="输入股票代码，用逗号分隔"
            )
        
        with col2:
            exchange = st.selectbox("交易所", ["SMART", "NYSE", "NASDAQ"], index=0)
        
        if st.button("📈 获取数据", type="secondary"):
            if symbols_input:
                symbols = [s.strip().upper() for s in symbols_input.split(',')]
                self._fetch_stock_data(symbols, exchange)
        
        # 显示演示数据
        st.markdown("---")
        st.markdown("#### 🎭 演示数据")
        
        if st.button("🎯 显示演示数据"):
            self._show_demo_data()
    
    def _test_connection(self):
        """测试IB连接"""
        with st.spinner("🔍 正在测试IB连接..."):
            try:
                # 创建事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # 运行连接测试
                result = loop.run_until_complete(self._async_test_connection())
                
                if result['success']:
                    st.success("✅ IB连接成功!")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        if result.get('accounts'):
                            st.info(f"📊 账户: {', '.join(result['accounts'])}")
                    
                    with col2:
                        if result.get('sample_price'):
                            st.info(f"📈 AAPL价格: ${result['sample_price']}")
                else:
                    st.error(f"❌ 连接失败: {result.get('error', '未知错误')}")
                    st.info("💡 请检查IB Gateway/TWS是否运行")
                
                loop.close()
                
            except Exception as e:
                st.error(f"❌ 测试过程中出错: {e}")
    
    async def _async_test_connection(self) -> Dict[str, Any]:
        """异步测试连接"""
        ib = IB()
        result = {'success': False}
        
        try:
            await ib.connectAsync(
                host=self.host,
                port=self.port,
                clientId=self.client_id,
                timeout=10
            )
            
            result['success'] = True
            
            # 获取账户信息
            try:
                accounts = ib.managedAccounts()
                result['accounts'] = accounts
            except:
                pass
            
            # 获取样本价格
            try:
                contract = Stock('AAPL', 'SMART', 'USD')
                ib.qualifyContracts(contract)
                ticker = ib.reqMktData(contract, '', False, False)
                await asyncio.sleep(2)
                if ticker.last:
                    result['sample_price'] = ticker.last
                ib.cancelMktData(contract)
            except:
                pass
            
        except Exception as e:
            result['error'] = str(e)
        
        finally:
            if ib.isConnected():
                ib.disconnect()
        
        return result
    
    def _fetch_stock_data(self, symbols: List[str], exchange: str):
        """获取股票数据"""
        with st.spinner(f"📊 正在获取 {len(symbols)} 只股票数据..."):
            try:
                # 创建事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # 运行数据获取
                data = loop.run_until_complete(
                    self._async_fetch_data(symbols, exchange)
                )
                
                if data:
                    self._display_stock_data(data)
                else:
                    st.error("❌ 未获取到数据")
                
                loop.close()
                
            except Exception as e:
                st.error(f"❌ 获取数据时出错: {e}")
    
    async def _async_fetch_data(self, symbols: List[str], exchange: str) -> List[Dict]:
        """异步获取数据"""
        ib = IB()
        data = []
        
        try:
            await ib.connectAsync(
                host=self.host,
                port=self.port,
                clientId=self.client_id,
                timeout=10
            )
            
            for symbol in symbols:
                try:
                    contract = Stock(symbol, exchange, 'USD')
                    ib.qualifyContracts(contract)
                    
                    if contract:
                        ticker = ib.reqMktData(contract, '', False, False)
                        await asyncio.sleep(2)
                        
                        stock_data = {
                            'symbol': symbol,
                            'price': ticker.last if ticker.last else 0,
                            'bid': ticker.bid if ticker.bid else 0,
                            'ask': ticker.ask if ticker.ask else 0,
                            'volume': ticker.volume if ticker.volume else 0,
                            'exchange': exchange,
                            'timestamp': datetime.now()
                        }
                        
                        data.append(stock_data)
                        ib.cancelMktData(contract)
                
                except Exception as e:
                    st.warning(f"⚠️ 获取 {symbol} 数据失败: {e}")
        
        except Exception as e:
            st.error(f"❌ IB连接失败: {e}")
        
        finally:
            if ib.isConnected():
                ib.disconnect()
        
        return data
    
    def _display_stock_data(self, data: List[Dict]):
        """显示股票数据"""
        st.success(f"✅ 成功获取 {len(data)} 只股票数据")
        
        if not data:
            return
        
        # 转换为DataFrame
        df = pd.DataFrame(data)
        
        # 显示数据表格
        st.markdown("##### 📋 股票数据表")
        
        # 格式化显示
        display_df = df.copy()
        display_df['price'] = display_df['price'].apply(lambda x: f"${x:.2f}" if x > 0 else "N/A")
        display_df['bid'] = display_df['bid'].apply(lambda x: f"${x:.2f}" if x > 0 else "N/A")
        display_df['ask'] = display_df['ask'].apply(lambda x: f"${x:.2f}" if x > 0 else "N/A")
        display_df['volume'] = display_df['volume'].apply(lambda x: f"{x:,}" if x > 0 else "N/A")
        display_df['timestamp'] = display_df['timestamp'].apply(lambda x: x.strftime('%H:%M:%S'))
        
        st.dataframe(display_df, use_container_width=True)
        
        # 显示指标卡片
        st.markdown("##### 📊 实时价格")
        
        cols = st.columns(min(len(data), 4))
        for i, row in enumerate(data):
            if i < 4:  # 最多显示4个
                with cols[i]:
                    price = row['price'] if row['price'] > 0 else 0
                    st.metric(
                        label=row['symbol'],
                        value=f"${price:.2f}" if price > 0 else "N/A",
                        delta=None
                    )
    
    def _show_demo_data(self):
        """显示演示数据"""
        st.info("🎭 以下是演示数据，展示IB数据的格式")
        
        # 创建演示数据
        demo_data = [
            {
                'symbol': 'AAPL',
                'price': 209.22,
                'bid': 209.20,
                'ask': 209.24,
                'volume': 45234567,
                'market_cap': 3200000000000,
                'pe_ratio': 28.5,
                'pb_ratio': 45.2,
                'exchange': 'NASDAQ',
                'timestamp': datetime.now()
            },
            {
                'symbol': 'MSFT',
                'price': 415.20,
                'bid': 415.18,
                'ask': 415.22,
                'volume': 23456789,
                'market_cap': 3100000000000,
                'pe_ratio': 32.1,
                'pb_ratio': 12.8,
                'exchange': 'NASDAQ',
                'timestamp': datetime.now()
            },
            {
                'symbol': 'GOOGL',
                'price': 175.80,
                'bid': 175.78,
                'ask': 175.82,
                'volume': 18765432,
                'market_cap': 2200000000000,
                'pe_ratio': 25.3,
                'pb_ratio': 5.9,
                'exchange': 'NASDAQ',
                'timestamp': datetime.now()
            }
        ]
        
        # 显示演示数据
        self._display_stock_data(demo_data)
        
        # 额外的演示信息
        st.markdown("##### 💡 功能说明")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("""
            **✅ 已实现功能:**
            - IB连接测试
            - 实时价格数据
            - 多股票查询
            - 数据格式化显示
            """)
        
        with col2:
            st.markdown("""
            **🚧 开发中功能:**
            - 基本面比率分析
            - 历史数据图表
            - 期权数据
            - 风险指标计算
            """)
        
        st.info("💡 完整的IB基本面数据功能请使用: `python scripts/test_ib_fundamentals.py`")


# 使用示例
def main():
    """测试UI组件"""
    st.set_page_config(
        page_title="IB基本面数据",
        page_icon="💎",
        layout="wide"
    )
    
    ui = IBSimpleUI()
    ui.render()


if __name__ == "__main__":
    main()
