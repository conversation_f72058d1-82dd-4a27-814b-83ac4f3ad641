{
  "name": "🔮 灵宝道君十二龙子验证系统",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "lingbao-twelve-dragons",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-trigger",
      "name": "🔮 灵宝道君验证触发",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "jsCode": "// 🐂 任务分发器 - 为十二龙子分配任务\nconst requestData = $input.first().json;\nconst claims = requestData.claims || [];\nconst dragonTasks = [];\n\n// 为每个论断创建十二龙子任务\nclaims.forEach((claim, index) => {\n  // 提取关键词\n  const keywords = claim.replace(/[，。！？]/g, ' ').split(' ').filter(word => word.length > 1);\n  \n  dragonTasks.push({\n    task_id: `task_${index + 1}`,\n    original_claim: claim,\n    keywords: keywords,\n    dragon_assignments: {\n      \"囚牛\": {\n        \"role\": \"基础搜索\",\n        \"queries\": [\n          `${keywords.slice(0, 3).join(' ')} 最新消息`,\n          `${keywords.slice(0, 2).join(' ')} 新闻`\n        ]\n      },\n      \"睚眦\": {\n        \"role\": \"深度挖掘\", \n        \"queries\": [\n          `${keywords[0]} 深度分析`,\n          `${keywords.slice(0, 2).join(' ')} 详细报告`\n        ]\n      },\n      \"狻猊\": {\n        \"role\": \"权威验证\",\n        \"queries\": [\n          `${keywords[0]} 官方 公告`,\n          `${keywords.slice(0, 2).join(' ')} 权威 报告`\n        ]\n      },\n      \"蒲牢\": {\n        \"role\": \"信号放大\",\n        \"keywords\": [\"重要\", \"关键\", \"突破\", \"重大\"]\n      },\n      \"嘲风\": {\n        \"role\": \"趋势分析\",\n        \"keywords\": [\"趋势\", \"预测\", \"未来\", \"展望\"]\n      },\n      \"狴犴\": {\n        \"role\": \"公正评估\",\n        \"keywords\": [\"客观\", \"中性\", \"平衡\", \"理性\"]\n      }\n    },\n    timestamp: new Date().toISOString()\n  });\n});\n\nreturn dragonTasks.map(task => ({ json: task }));"
      },
      "id": "task-distributor",
      "name": "🐂 任务分发器",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [460, 300]
    },
    {
      "parameters": {
        "url": "https://www.google.com/search",
        "sendQuery": true,
        "queryParameters": {
          "parameters": [
            {
              "name": "q",
              "value": "={{ $json.dragon_assignments.囚牛.queries[0] }}"
            },
            {
              "name": "num",
              "value": "10"
            },
            {
              "name": "hl",
              "value": "zh-CN"
            }
          ]
        },
        "options": {
          "timeout": 10000
        }
      },
      "id": "qiuniu-search",
      "name": "🐂 囚牛 - 基础搜索",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [680, 200]
    },
    {
      "parameters": {
        "url": "https://cn.investing.com/search/",
        "sendQuery": true,
        "queryParameters": {
          "parameters": [
            {
              "name": "q",
              "value": "={{ $json.dragon_assignments.睚眦.queries[0] }}"
            }
          ]
        },
        "options": {
          "timeout": 10000
        }
      },
      "id": "yaizi-search",
      "name": "🐺 睚眦 - 深度挖掘",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [680, 300]
    },
    {
      "parameters": {
        "url": "https://finance.sina.com.cn/search/",
        "sendQuery": true,
        "queryParameters": {
          "parameters": [
            {
              "name": "q",
              "value": "={{ $json.dragon_assignments.狻猊.queries[0] }}"
            }
          ]
        },
        "options": {
          "timeout": 10000
        }
      },
      "id": "suanni-search",
      "name": "🦁 狻猊 - 权威验证",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [680, 400]
    },
    {
      "parameters": {
        "jsCode": "// 🔔 蒲牢 - 信号放大器\nconst taskData = $input.first().json;\nconst searchResults = $input.all().slice(1); // 排除第一个任务数据\n\nfunction amplifySignals(results, keywords) {\n  let amplifiedResults = [];\n  let importanceScore = 0;\n  \n  results.forEach(result => {\n    const content = result.json.data || '';\n    const contentLower = content.toLowerCase();\n    \n    // 检查重要信号词\n    keywords.forEach(keyword => {\n      if (contentLower.includes(keyword)) {\n        importanceScore += 0.2;\n        amplifiedResults.push({\n          signal: keyword,\n          context: extractContext(content, keyword),\n          importance: 'HIGH'\n        });\n      }\n    });\n  });\n  \n  return {\n    dragon: \"蒲牢\",\n    role: \"信号放大\",\n    importance_score: Math.min(importanceScore, 1.0),\n    amplified_signals: amplifiedResults,\n    signal_count: amplifiedResults.length\n  };\n}\n\nfunction extractContext(content, keyword) {\n  const index = content.toLowerCase().indexOf(keyword);\n  if (index === -1) return '';\n  \n  const start = Math.max(0, index - 50);\n  const end = Math.min(content.length, index + 50);\n  return content.substring(start, end);\n}\n\nconst amplificationResult = amplifySignals(\n  searchResults, \n  taskData.dragon_assignments.蒲牢.keywords\n);\n\nreturn [{ json: { ...taskData, ...amplificationResult } }];"
      },
      "id": "pulao-amplifier",
      "name": "🔔 蒲牢 - 信号放大",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 250]
    },
    {
      "parameters": {
        "jsCode": "// 🦅 嘲风 - 趋势分析器\nconst inputData = $input.first().json;\n\nfunction analyzeTrends(data) {\n  const trendKeywords = data.dragon_assignments.嘲风.keywords;\n  let trendScore = 0.5; // 基础趋势分数\n  let trendDirection = 'NEUTRAL';\n  let trendStrength = 'MEDIUM';\n  \n  // 分析趋势方向\n  const positiveWords = ['增长', '上涨', '看好', '乐观', '强劲'];\n  const negativeWords = ['下跌', '下降', '悲观', '风险', '担忧'];\n  \n  let positiveCount = 0;\n  let negativeCount = 0;\n  \n  // 模拟趋势分析（实际应用中会分析搜索结果内容）\n  const content = JSON.stringify(data).toLowerCase();\n  \n  positiveWords.forEach(word => {\n    if (content.includes(word)) positiveCount++;\n  });\n  \n  negativeWords.forEach(word => {\n    if (content.includes(word)) negativeCount++;\n  });\n  \n  // 计算趋势方向和强度\n  if (positiveCount > negativeCount) {\n    trendDirection = 'POSITIVE';\n    trendScore = 0.5 + (positiveCount - negativeCount) * 0.1;\n  } else if (negativeCount > positiveCount) {\n    trendDirection = 'NEGATIVE';\n    trendScore = 0.5 - (negativeCount - positiveCount) * 0.1;\n  }\n  \n  trendScore = Math.max(0, Math.min(1, trendScore));\n  \n  if (Math.abs(positiveCount - negativeCount) >= 3) {\n    trendStrength = 'STRONG';\n  } else if (Math.abs(positiveCount - negativeCount) >= 1) {\n    trendStrength = 'MEDIUM';\n  } else {\n    trendStrength = 'WEAK';\n  }\n  \n  return {\n    dragon: \"嘲风\",\n    role: \"趋势分析\",\n    trend_direction: trendDirection,\n    trend_strength: trendStrength,\n    trend_score: trendScore,\n    positive_signals: positiveCount,\n    negative_signals: negativeCount,\n    analysis_summary: `趋势${trendDirection}，强度${trendStrength}，评分${trendScore.toFixed(2)}`\n  };\n}\n\nconst trendAnalysis = analyzeTrends(inputData);\n\nreturn [{ json: { ...inputData, ...trendAnalysis } }];"
      },
      "id": "chaofeng-analyzer",
      "name": "🦅 嘲风 - 趋势分析",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1120, 250]
    },
    {
      "parameters": {
        "jsCode": "// 🐅 狴犴 - 公正评估器\nconst inputData = $input.first().json;\n\nfunction fairAssessment(data) {\n  // 公正性评估算法\n  const signals = data.amplified_signals || [];\n  const trendScore = data.trend_score || 0.5;\n  \n  let biasScore = 0;\n  let objectivityScore = 0.5;\n  let balanceScore = 0.5;\n  \n  // 检查信息平衡性\n  const positiveSignals = signals.filter(s => \n    s.signal && ['重要', '关键', '突破'].includes(s.signal)\n  ).length;\n  \n  const totalSignals = signals.length;\n  \n  if (totalSignals > 0) {\n    balanceScore = 1 - Math.abs(positiveSignals / totalSignals - 0.5) * 2;\n  }\n  \n  // 计算客观性分数\n  objectivityScore = (balanceScore + (1 - Math.abs(trendScore - 0.5) * 2)) / 2;\n  \n  // 偏见检测\n  if (objectivityScore < 0.3) {\n    biasScore = 0.8; // 高偏见\n  } else if (objectivityScore < 0.6) {\n    biasScore = 0.4; // 中等偏见\n  } else {\n    biasScore = 0.1; // 低偏见\n  }\n  \n  return {\n    dragon: \"狴犴\",\n    role: \"公正评估\",\n    objectivity_score: objectivityScore,\n    balance_score: balanceScore,\n    bias_score: biasScore,\n    fairness_rating: objectivityScore >= 0.7 ? 'HIGH' : \n                    objectivityScore >= 0.4 ? 'MEDIUM' : 'LOW',\n    assessment_summary: `客观性${objectivityScore.toFixed(2)}，平衡性${balanceScore.toFixed(2)}，偏见度${biasScore.toFixed(2)}`\n  };\n}\n\nconst fairnessAssessment = fairAssessment(inputData);\n\nreturn [{ json: { ...inputData, ...fairnessAssessment } }];"
      },
      "id": "bian-assessor",
      "name": "🐅 狴犴 - 公正评估",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1340, 250]
    },
    {
      "parameters": {
        "jsCode": "// 🐉 饕餮 - 最终决策者\nconst inputData = $input.first().json;\n\nfunction finalDecision(data) {\n  // 综合所有龙子的分析结果\n  const weights = {\n    trend_score: 0.3,\n    importance_score: 0.2,\n    objectivity_score: 0.2,\n    balance_score: 0.15,\n    signal_count: 0.1,\n    bias_penalty: 0.05\n  };\n  \n  let finalConfidence = 0;\n  \n  // 加权计算最终置信度\n  finalConfidence += (data.trend_score || 0.5) * weights.trend_score;\n  finalConfidence += (data.importance_score || 0.5) * weights.importance_score;\n  finalConfidence += (data.objectivity_score || 0.5) * weights.objectivity_score;\n  finalConfidence += (data.balance_score || 0.5) * weights.balance_score;\n  \n  // 信号数量加分\n  const signalBonus = Math.min((data.signal_count || 0) * 0.05, 0.1);\n  finalConfidence += signalBonus;\n  \n  // 偏见惩罚\n  const biasPenalty = (data.bias_score || 0) * weights.bias_penalty;\n  finalConfidence -= biasPenalty;\n  \n  // 限制在0-1范围内\n  finalConfidence = Math.max(0, Math.min(1, finalConfidence));\n  \n  // 生成最终建议\n  let recommendation = '';\n  let riskLevel = '';\n  \n  if (finalConfidence >= 0.8) {\n    recommendation = 'STRONG_APPROVE';\n    riskLevel = 'LOW';\n  } else if (finalConfidence >= 0.6) {\n    recommendation = 'APPROVE';\n    riskLevel = 'MEDIUM';\n  } else if (finalConfidence >= 0.4) {\n    recommendation = 'REVIEW_REQUIRED';\n    riskLevel = 'MEDIUM';\n  } else {\n    recommendation = 'REJECT';\n    riskLevel = 'HIGH';\n  }\n  \n  return {\n    verification_id: `lingbao_${Date.now()}`,\n    original_claim: data.original_claim,\n    dragon_summary: {\n      \"囚牛\": \"基础搜索完成\",\n      \"睚眦\": \"深度挖掘完成\", \n      \"狻猊\": \"权威验证完成\",\n      \"蒲牢\": `信号放大完成，重要性${data.importance_score?.toFixed(2)}\`,\n      \"嘲风\": `趋势分析完成，${data.trend_direction}\`,\n      \"狴犴\": `公正评估完成，${data.fairness_rating}\`,\n      \"饕餮\": \"最终决策完成\"\n    },\n    final_confidence: finalConfidence,\n    recommendation: recommendation,\n    risk_level: riskLevel,\n    confidence_breakdown: {\n      trend_contribution: (data.trend_score || 0.5) * weights.trend_score,\n      importance_contribution: (data.importance_score || 0.5) * weights.importance_score,\n      objectivity_contribution: (data.objectivity_score || 0.5) * weights.objectivity_score,\n      signal_bonus: signalBonus,\n      bias_penalty: biasPenalty\n    },\n    timestamp: new Date().toISOString(),\n    summary: `十二龙子协同验证完成，最终置信度${finalConfidence.toFixed(2)}，建议${recommendation}`\n  };\n}\n\nconst finalResult = finalDecision(inputData);\n\nreturn [{ json: finalResult }];"
      },
      "id": "taotie-decision",
      "name": "🐉 饕餮 - 最终决策",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1560, 250]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ $json }}"
      },
      "id": "response-node",
      "name": "📋 验证结果返回",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [1780, 250]
    }
  ],
  "connections": {
    "webhook-trigger": {
      "main": [
        [
          {
            "node": "task-distributor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "task-distributor": {
      "main": [
        [
          {
            "node": "qiuniu-search",
            "type": "main",
            "index": 0
          },
          {
            "node": "yaizi-search", 
            "type": "main",
            "index": 0
          },
          {
            "node": "suanni-search",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "qiuniu-search": {
      "main": [
        [
          {
            "node": "pulao-amplifier",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "yaizi-search": {
      "main": [
        [
          {
            "node": "pulao-amplifier",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "suanni-search": {
      "main": [
        [
          {
            "node": "pulao-amplifier",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "pulao-amplifier": {
      "main": [
        [
          {
            "node": "chaofeng-analyzer",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "chaofeng-analyzer": {
      "main": [
        [
          {
            "node": "bian-assessor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "bian-assessor": {
      "main": [
        [
          {
            "node": "taotie-decision",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "taotie-decision": {
      "main": [
        [
          {
            "node": "response-node",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": ["灵宝道君", "十二龙子", "投资验证"],
  "triggerCount": 0,
  "updatedAt": "2025-01-13T09:00:00.000Z",
  "versionId": "1"
}
