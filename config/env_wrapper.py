#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境变量包装器
自动加载配置的便捷模块
"""

import os
from pathlib import Path

# 自动加载配置
def load_config():
    """自动加载配置"""
    try:
        from scripts.hybrid_config_loader import HybridConfigLoader
        loader = HybridConfigLoader()
        loader.load_config()
        return True
    except Exception as e:
        print(f"配置加载失败: {e}")
        return False

# 在模块导入时自动加载
_config_loaded = load_config()

def get_env(key: str, default: str = None) -> str:
    """获取环境变量的便捷函数"""
    return os.getenv(key, default)

def require_env(key: str) -> str:
    """获取必需的环境变量"""
    value = os.getenv(key)
    if not value:
        raise ValueError(f"必需的环境变量 {key} 未设置")
    return value

# 常用配置的快捷访问
DATABASE_URL = get_env("DATABASE_URL")
ZILLIZ_TOKEN = get_env("ZILLIZ_TOKEN")
OPENROUTER_API_KEY_1 = get_env("OPENROUTER_API_KEY_1")
MASTODON_ACCESS_TOKEN = get_env("MASTODON_ACCESS_TOKEN")
ANTHROPIC_AUTH_TOKEN = get_env("ANTHROPIC_AUTH_TOKEN")
