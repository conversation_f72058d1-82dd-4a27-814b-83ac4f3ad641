#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启用Doppler模式脚本
修改项目配置以优先使用Doppler，同时保持.env文件作为备份
"""

import os
import shutil
from pathlib import Path

def backup_env_file():
    """备份.env文件"""
    project_root = Path(__file__).parent.parent
    env_file = project_root / ".env"
    backup_file = project_root / ".env.backup"
    
    if env_file.exists():
        shutil.copy2(env_file, backup_file)
        print(f"✅ .env文件已备份到: {backup_file}")
        return True
    else:
        print("❌ .env文件不存在")
        return False

def create_doppler_env():
    """创建Doppler环境配置文件"""
    project_root = Path(__file__).parent.parent
    doppler_env = project_root / ".env.doppler"
    
    content = """# Doppler配置文件
# 这个文件指示项目优先使用Doppler
DOPPLER_ENABLED=true
DOPPLER_PROJECT=cauldron
DOPPLER_CONFIG=development

# 如果Doppler不可用，回退到.env文件
FALLBACK_TO_DOTENV=true

# Doppler tokens (从原.env文件复制)
DOPPLER_TOKEN=*************************************************
DOPPLER_SERVICE_TOKEN=*************************************************
"""
    
    with open(doppler_env, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Doppler配置文件已创建: {doppler_env}")

def update_config_manager():
    """更新ConfigManager以优先使用Doppler"""
    config_file = Path(__file__).parent.parent / "src/core/config_manager.py"
    
    if not config_file.exists():
        print("❌ ConfigManager文件不存在")
        return False
    
    # 读取现有内容
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经更新过
    if "DOPPLER_ENABLED" in content:
        print("✅ ConfigManager已经支持Doppler优先模式")
        return True
    
    # 在_detect_doppler方法中添加环境变量检查
    old_detect = """    def _detect_doppler(self) -> bool:
        \"\"\"自动检测是否可以使用Doppler\"\"\"
        try:
            # 检查Doppler CLI是否安装
            result = subprocess.run(['doppler', '--version'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                # 检查是否已配置项目
                result = subprocess.run(['doppler', 'configure', 'get'],
                                      capture_output=True, text=True, timeout=5)
                return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        return False"""
    
    new_detect = """    def _detect_doppler(self) -> bool:
        \"\"\"自动检测是否可以使用Doppler\"\"\"
        # 首先检查环境变量
        if os.getenv("DOPPLER_ENABLED", "").lower() == "true":
            print("🔐 环境变量指示使用Doppler")
            return True
            
        try:
            # 检查Doppler CLI是否安装
            result = subprocess.run(['doppler', '--version'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                # 检查是否已配置项目
                result = subprocess.run(['doppler', 'configure', 'get'],
                                      capture_output=True, text=True, timeout=5)
                return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        return False"""
    
    # 替换内容
    content = content.replace(old_detect, new_detect)
    
    # 写回文件
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ ConfigManager已更新以支持Doppler优先模式")
    return True

def create_run_scripts():
    """创建Doppler运行脚本"""
    project_root = Path(__file__).parent.parent
    scripts_dir = project_root / "scripts"
    
    # 创建Streamlit启动脚本
    streamlit_script = scripts_dir / "run_streamlit_doppler.sh"
    streamlit_content = """#!/bin/bash
# 使用Doppler运行Streamlit应用

echo "🚀 使用Doppler启动Streamlit应用..."

# 检查Doppler配置
if ! doppler configure get > /dev/null 2>&1; then
    echo "⚠️ Doppler未配置，使用传统.env文件"
    streamlit run app/streamlit_app.py
else
    echo "🔐 使用Doppler运行"
    doppler run -- streamlit run app/streamlit_app.py
fi
"""
    
    with open(streamlit_script, 'w', encoding='utf-8') as f:
        f.write(streamlit_content)
    
    # 设置执行权限
    os.chmod(streamlit_script, 0o755)
    print(f"✅ Streamlit Doppler脚本已创建: {streamlit_script}")
    
    # 创建稷下学宫启动脚本
    jixia_script = scripts_dir / "run_jixia_doppler.sh"
    jixia_content = """#!/bin/bash
# 使用Doppler运行稷下学宫

echo "🎭 使用Doppler启动稷下学宫..."

# 检查Doppler配置
if ! doppler configure get > /dev/null 2>&1; then
    echo "⚠️ Doppler未配置，使用传统.env文件"
    python scripts/start_jixia_academy.py
else
    echo "🔐 使用Doppler运行"
    doppler run -- python scripts/start_jixia_academy.py
fi
"""
    
    with open(jixia_script, 'w', encoding='utf-8') as f:
        f.write(jixia_content)
    
    # 设置执行权限
    os.chmod(jixia_script, 0o755)
    print(f"✅ 稷下学宫Doppler脚本已创建: {jixia_script}")

def update_procfile():
    """更新Procfile以使用Doppler"""
    project_root = Path(__file__).parent.parent
    procfile = project_root / "Procfile"
    
    if procfile.exists():
        with open(procfile, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 如果已经使用doppler run，跳过
        if "doppler run" in content:
            print("✅ Procfile已经配置为使用Doppler")
            return
        
        # 备份原Procfile
        backup_procfile = project_root / "Procfile.backup"
        shutil.copy2(procfile, backup_procfile)
        print(f"✅ Procfile已备份到: {backup_procfile}")
        
        # 更新Procfile
        lines = content.strip().split('\n')
        updated_lines = []
        
        for line in lines:
            if line.strip() and not line.startswith('#'):
                # 在命令前添加doppler run --
                if ':' in line:
                    process_type, command = line.split(':', 1)
                    updated_line = f"{process_type}: doppler run --{command}"
                    updated_lines.append(updated_line)
                else:
                    updated_lines.append(line)
            else:
                updated_lines.append(line)
        
        # 写回文件
        with open(procfile, 'w', encoding='utf-8') as f:
            f.write('\n'.join(updated_lines))
        
        print("✅ Procfile已更新以使用Doppler")

def main():
    """主函数"""
    print("🔐 启用Cauldron项目Doppler模式")
    print("=" * 40)
    
    # 1. 备份.env文件
    if not backup_env_file():
        return False
    
    # 2. 创建Doppler配置
    create_doppler_env()
    
    # 3. 更新ConfigManager
    update_config_manager()
    
    # 4. 创建运行脚本
    create_run_scripts()
    
    # 5. 更新Procfile
    update_procfile()
    
    print("\n✅ Doppler模式启用完成！")
    print("\n🔧 下一步:")
    print("1. 设置环境变量: export DOPPLER_ENABLED=true")
    print("2. 运行应用: ./scripts/run_streamlit_doppler.sh")
    print("3. 或者: ./scripts/run_jixia_doppler.sh")
    print("4. 验证配置: python scripts/verify_doppler.py")
    
    print("\n📝 注意:")
    print("- 原.env文件已备份为.env.backup")
    print("- 如果Doppler不可用，系统会自动回退到.env文件")
    print("- 所有脚本都支持Doppler和传统模式")
    
    return True

if __name__ == "__main__":
    main()
