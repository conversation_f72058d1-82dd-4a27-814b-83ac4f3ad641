# Internal instruction for AI Agent
internal_instruction/

# Environment variables
.env
*.env
.env.*
!.env.example

# Docker data directories
jixia_academy/data/
jixia_academy/logs/
jixia_academy/backups/
jixia_academy/exports/
jixia_academy/reports/

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.venv/
venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 太公心易项目特定忽略文件
# ================================

# 调试文件 - 禁止在根目录创建
/*_test.py
/*_debug.py
/*_demo.py
/test_*.py
/debug_*.py
/auto_*.py
/simple_*.py
/standalone_*.py
/terminal_*.py

# 测试结果文件
/test_results*.json
/test_results*.log
/feedburner_test_results*.json
/rss_test_results*.json
/test_report*.json

# 临时文件
/temp_*.py
/tmp_*.py
/backup_*.py

# 日志文件
*.log

# 注意：调试和测试文件应该放在以下目录：
# - scripts/ (脚本文件)
# - tests/ (测试文件)
# - temp/ (临时文件)
# - examples/ (示例文件)
# 稷下学宫大清理后的新规则
# ================================

# 归档目录
archive/
docs_backup_*/

# 临时和测试文件
tmp_*
test_*
debug_*
*_test.*
*_debug.*
*_demo.*
simple_*

# 开发文档和笔记
INTERNAL_*.md
TODO_*.md
*_notes.md
*_draft.md
DEVELOPMENT_LOG.md

# 配置文件中的敏感信息
mcp_client_config.json
lingbao_mcp_test_report.json

# 工作区管理文件
cleanup_*.sh
organize_*.sh
file_lifecycle_policy.md
github_deployment_plan.md

# Ansible开发文件
ansible/
test-*.yml
test-*.sh

# 大文件和数据
*.db
*.sqlite
data/
logs/
models/
*.pkl
*.parquet

mcp-server-milvus/
