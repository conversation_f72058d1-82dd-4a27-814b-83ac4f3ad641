from abc import ABC, abstractmethod
from typing import List

class AbstractCycleModel(ABC):
    """
    周期模型的抽象基类 (Abstract Base Class)。
    它定义了一份“合同”，任何周期系统（如十二长生、西洋十二宫）都必须遵守。
    这使得上层应用（如FSM）可以与具体的周期模型解耦。
    """
    @property
    @abstractmethod
    def name(self) -> str:
        """周期模型的名称。"""
        pass

    @property
    @abstractmethod
    def stages(self) -> List[str]:
        """一个包含所有周期阶段名称的列表。"""
        pass

    def __len__(self) -> int:
        """返回周期阶段的总数。"""
        return len(self.stages)

class TwelveLifeStagesModel(AbstractCycleModel):
    """“十二长生”周期的具体实现。"""
    @property
    def name(self) -> str:
        return "十二长生"

    @property
    def stages(self) -> List[str]:
        return ["长生", "沐浴", "冠带", "临官", "帝旺", "衰", "病", "死", "墓", "绝", "胎", "养"]

class ZodiacModel(AbstractCycleModel):
    """“西洋十二宫”周期的具体实现，用于演示扩展性。"""
    @property
    def name(self) -> str:
        return "西洋十二宫"

    @property
    def stages(self) -> List[str]:
        return ["Aries", "Taurus", "Gemini", "Cancer", "Leo", "Virgo", "Libra", "Scorpio", "Sagittarius", "Capricorn", "Aquarius", "Pisces"]

# 提供一个默认实例，方便系统在未指定时使用
DEFAULT_CYCLE_MODEL = TwelveLifeStagesModel()