{"name": "每日RSS悬丝诊脉 → Zilliz唯一真理", "nodes": [{"parameters": {"httpMethod": "POST", "path": "daily-analysis", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook触发器", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "daily-analysis-trigger"}, {"parameters": {"url": "https://xueqiu.com/statuses/public_timeline_by_category.json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {"timeout": 10000}}, "id": "fetch-xueqiu-rss", "name": "获取雪球RSS", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 200]}, {"parameters": {"url": "https://36kr.com/api/feed", "options": {"timeout": 10000}}, "id": "fetch-36kr-rss", "name": "获取36Kr RSS", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"url": "https://wallstreetcn.com/api/v1/apiv1/content/articles", "options": {"timeout": 10000}}, "id": "fetch-wallstreet-rss", "name": "获取华尔街见闻RSS", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 400]}, {"parameters": {"jsCode": "// RSS数据清洗和标准化\nconst items = [];\n\n// 处理所有输入数据\nfor (const inputData of $input.all()) {\n  const source = inputData.json;\n  let articles = [];\n  \n  // 根据不同源解析数据结构\n  if (inputData.node === 'fetch-xueqiu-rss') {\n    articles = source.statuses || [];\n    articles = articles.map(item => ({\n      title: item.text?.substring(0, 100) || '',\n      content: item.text || '',\n      source: 'xueqiu',\n      timestamp: new Date(item.created_at).toISOString(),\n      url: `https://xueqiu.com/statuses/${item.id}`,\n      author: item.user?.screen_name || 'unknown'\n    }));\n  } else if (inputData.node === 'fetch-36kr-rss') {\n    articles = source.data?.items || [];\n    articles = articles.map(item => ({\n      title: item.title || '',\n      content: item.summary || item.content || '',\n      source: '36kr',\n      timestamp: new Date(item.published_at).toISOString(),\n      url: item.url || '',\n      author: item.author?.name || 'unknown'\n    }));\n  } else if (inputData.node === 'fetch-wallstreet-rss') {\n    articles = source.data?.items || [];\n    articles = articles.map(item => ({\n      title: item.title || '',\n      content: item.content || item.summary || '',\n      source: 'wallstreetcn',\n      timestamp: new Date(item.display_time).toISOString(),\n      url: item.uri || '',\n      author: 'wallstreetcn'\n    }));\n  }\n  \n  // 过滤和清洗\n  articles = articles.filter(article => \n    article.title.length > 10 && \n    article.content.length > 20 &&\n    !article.title.includes('广告') &&\n    !article.content.includes('推广')\n  );\n  \n  items.push(...articles);\n}\n\n// 去重和排序\nconst uniqueItems = items.filter((item, index, self) => \n  index === self.findIndex(t => t.title === item.title)\n);\n\nunique Items.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));\n\n// 只保留最新的50条\nreturn uniqueItems.slice(0, 50).map(item => ({ json: item }));"}, "id": "clean-rss-data", "name": "RSS数据清洗", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"model": "text-embedding-3-small", "options": {"temperature": 0}}, "id": "generate-embeddings", "name": "生成文本向量", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [900, 300], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"jsCode": "// 情绪分析和关键词提取\nconst items = [];\n\nfor (const inputData of $input.all()) {\n  const article = inputData.json;\n  \n  // 简单的情绪分析（实际应该调用专业模型）\n  const content = article.content.toLowerCase();\n  let sentiment = 0;\n  \n  // 正面词汇\n  const positiveWords = ['上涨', '利好', '突破', '增长', '机会', '看好', '买入'];\n  const negativeWords = ['下跌', '利空', '风险', '下滑', '担忧', '看空', '卖出'];\n  \n  positiveWords.forEach(word => {\n    if (content.includes(word)) sentiment += 0.1;\n  });\n  \n  negativeWords.forEach(word => {\n    if (content.includes(word)) sentiment -= 0.1;\n  });\n  \n  // 限制在-1到1之间\n  sentiment = Math.max(-1, Math.min(1, sentiment));\n  \n  // 关键词提取（简化版）\n  const keywords = [];\n  const keywordPatterns = [\n    '科技股', 'AI', '人工智能', '新能源', '芯片',\n    '银行', '保险', '地产', '消费', '医药',\n    '央行', '利率', '政策', '通胀', 'GDP'\n  ];\n  \n  keywordPatterns.forEach(keyword => {\n    if (content.includes(keyword)) {\n      keywords.push(keyword);\n    }\n  });\n  \n  // 构建最终数据\n  const processedItem = {\n    ...article,\n    sentiment: sentiment,\n    keywords: keywords,\n    processed_at: new Date().toISOString(),\n    date: new Date().toISOString().split('T')[0],\n    vector: article.embedding || [] // 来自上一步的向量\n  };\n  \n  items.push({ json: processedItem });\n}\n\nreturn items;"}, "id": "sentiment-analysis", "name": "情绪分析", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"operation": "insert", "collection": "daily_market_intelligence", "fields": {"content": "={{ $json.content }}", "title": "={{ $json.title }}", "source": "={{ $json.source }}", "sentiment": "={{ $json.sentiment }}", "keywords": "={{ $json.keywords }}", "timestamp": "={{ $json.timestamp }}", "date": "={{ $json.date }}", "url": "={{ $json.url }}", "vector": "={{ $json.vector }}", "processed_at": "={{ $json.processed_at }}"}}, "id": "insert-to-zilliz", "name": "插入<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.milvus", "typeVersion": 1, "position": [1340, 300], "credentials": {"milvus": {"id": "zilliz-credentials", "name": "<PERSON>illiz <PERSON>"}}}, {"parameters": {"jsCode": "// 生成每日情报摘要\nconst allItems = $input.all();\nconst summary = {\n  date: new Date().toISOString().split('T')[0],\n  total_articles: allItems.length,\n  sources: [...new Set(allItems.map(item => item.json.source))],\n  average_sentiment: allItems.reduce((sum, item) => sum + item.json.sentiment, 0) / allItems.length,\n  sentiment_distribution: {\n    positive: allItems.filter(item => item.json.sentiment > 0.1).length,\n    neutral: allItems.filter(item => Math.abs(item.json.sentiment) <= 0.1).length,\n    negative: allItems.filter(item => item.json.sentiment < -0.1).length\n  },\n  top_keywords: (() => {\n    const keywordCount = {};\n    allItems.forEach(item => {\n      item.json.keywords.forEach(keyword => {\n        keywordCount[keyword] = (keywordCount[keyword] || 0) + 1;\n      });\n    });\n    return Object.entries(keywordCount)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 10)\n      .map(([keyword, count]) => ({ keyword, count }));\n  })()\n};\n\nreturn [{ json: summary }];"}, "id": "generate-summary", "name": "生成每日摘要", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"url": "https://cauldron.herokuapp.com/api/n8n/daily-summary", "httpMethod": "POST", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json }}", "options": {"timeout": 30000}}, "id": "notify-autogen", "name": "通知AutoGen", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1780, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"success\": true, \"processed_items\": $('insert-to-zilliz').itemMatching(0).$json.length, \"summary\": $('generate-summary').first().$json, \"timestamp\": new Date().toISOString() } }}"}, "id": "webhook-response", "name": "返回结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2000, 300]}], "connections": {"webhook-trigger": {"main": [[{"node": "fetch-xueqiu-rss", "type": "main", "index": 0}, {"node": "fetch-36kr-rss", "type": "main", "index": 0}, {"node": "fetch-wallstreet-rss", "type": "main", "index": 0}]]}, "fetch-xueqiu-rss": {"main": [[{"node": "clean-rss-data", "type": "main", "index": 0}]]}, "fetch-36kr-rss": {"main": [[{"node": "clean-rss-data", "type": "main", "index": 0}]]}, "fetch-wallstreet-rss": {"main": [[{"node": "clean-rss-data", "type": "main", "index": 0}]]}, "clean-rss-data": {"main": [[{"node": "generate-embeddings", "type": "main", "index": 0}]]}, "generate-embeddings": {"main": [[{"node": "sentiment-analysis", "type": "main", "index": 0}]]}, "sentiment-analysis": {"main": [[{"node": "insert-to-zilliz", "type": "main", "index": 0}]]}, "insert-to-zilliz": {"main": [[{"node": "generate-summary", "type": "main", "index": 0}]]}, "generate-summary": {"main": [[{"node": "notify-autogen", "type": "main", "index": 0}]]}, "notify-autogen": {"main": [[{"node": "webhook-response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"id": "rss-analysis", "name": "RSS分析"}, {"id": "daily-workflow", "name": "每日工作流"}], "triggerCount": 1, "updatedAt": "2025-01-06T22:30:00.000Z", "versionId": "1"}