#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置同步工具
在Doppler和.env文件之间同步配置
"""

import os
import re
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple
from datetime import datetime

class ConfigSync:
    """配置同步工具"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.env_file = self.project_root / ".env"
        self.backup_dir = self.project_root / "config" / "backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def check_doppler_available(self) -> bool:
        """检查Doppler是否可用"""
        try:
            result = subprocess.run(['doppler', '--version'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                return False
                
            result = subprocess.run(['doppler', 'configure', 'get'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def parse_env_file(self) -> Dict[str, str]:
        """解析.env文件"""
        env_vars = {}
        
        if not self.env_file.exists():
            print(f"❌ .env文件不存在: {self.env_file}")
            return env_vars
        
        with open(self.env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # 跳过注释和空行
                if not line or line.startswith('#'):
                    continue
                
                # 匹配 KEY=VALUE 格式
                match = re.match(r'^([A-Za-z_][A-Za-z0-9_]*)\s*=\s*(.*)$', line)
                if match:
                    key, value = match.groups()
                    
                    # 移除引号
                    value = value.strip('\'"')
                    
                    # 跳过空值和注释
                    if value and not value.startswith('#'):
                        env_vars[key] = value
        
        return env_vars
    
    def get_doppler_secrets(self) -> Dict[str, str]:
        """获取Doppler密钥"""
        try:
            result = subprocess.run(['doppler', 'secrets', 'download', '--no-file', '--format', 'json'],
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                print(f"❌ 获取Doppler密钥失败: {result.stderr}")
                return {}
        except Exception as e:
            print(f"❌ 获取Doppler密钥异常: {e}")
            return {}
    
    def backup_env_file(self) -> str:
        """备份.env文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = self.backup_dir / f".env.backup_{timestamp}"
        
        if self.env_file.exists():
            import shutil
            shutil.copy2(self.env_file, backup_file)
            print(f"✅ .env文件已备份到: {backup_file}")
            return str(backup_file)
        
        return ""
    
    def sync_to_doppler(self) -> bool:
        """将.env文件同步到Doppler"""
        if not self.check_doppler_available():
            print("❌ Doppler不可用，无法同步")
            return False
        
        env_vars = self.parse_env_file()
        if not env_vars:
            print("❌ 没有找到有效的环境变量")
            return False
        
        print(f"🚀 开始同步 {len(env_vars)} 个配置到Doppler...")
        
        success_count = 0
        error_count = 0
        
        for key, value in env_vars.items():
            try:
                # 跳过Doppler自身的配置
                if key.startswith('DOPPLER_'):
                    continue
                
                cmd = ['doppler', 'secrets', 'set', f'{key}={value}']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    print(f"  ✅ {key}")
                    success_count += 1
                else:
                    print(f"  ❌ {key}: {result.stderr.strip()}")
                    error_count += 1
                    
            except Exception as e:
                print(f"  ❌ {key}: {e}")
                error_count += 1
        
        print(f"\n📊 同步结果:")
        print(f"  ✅ 成功: {success_count}")
        print(f"  ❌ 失败: {error_count}")
        
        return success_count > 0
    
    def sync_from_doppler(self) -> bool:
        """从Doppler同步到.env文件"""
        if not self.check_doppler_available():
            print("❌ Doppler不可用，无法同步")
            return False
        
        # 备份现有.env文件
        backup_file = self.backup_env_file()
        
        # 获取Doppler密钥
        doppler_secrets = self.get_doppler_secrets()
        if not doppler_secrets:
            print("❌ 无法获取Doppler密钥")
            return False
        
        print(f"🚀 开始从Doppler同步 {len(doppler_secrets)} 个配置...")
        
        # 生成新的.env文件
        env_content = []
        env_content.append("# ===========================================")
        env_content.append("# Cauldron项目环境变量")
        env_content.append(f"# 从Doppler同步于: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        env_content.append("# ===========================================")
        env_content.append("")
        
        # 按类别组织配置
        categories = {
            "数据库配置": ["DATABASE_URL", "ZILLIZ_", "MONGO_", "MILVUS_", "SUPABASE_", "NEON_"],
            "AI服务": ["OPENROUTER_", "ANTHROPIC_", "GEMINI_", "HF_TOKEN", "LITELLM_"],
            "金融API": ["finnhub", "coingecko", "coindesk", "blockchair", "ALPHA_VANTAGE", "RAPIDAPI", "coinbase_"],
            "集成服务": ["TAVILY_", "FIRECRAWL_", "n8n_", "N8N_", "MASTODON_"],
            "云服务": ["UPSTASH_", "QSTASH_", "HEROKU_"],
            "基础设施": ["IB_", "APP_NAME", "PORT"]
        }
        
        categorized_vars = {cat: [] for cat in categories}
        uncategorized_vars = []
        
        for key, value in doppler_secrets.items():
            categorized = False
            for category, prefixes in categories.items():
                if any(key.startswith(prefix) or prefix.rstrip('_') == key for prefix in prefixes):
                    categorized_vars[category].append((key, value))
                    categorized = True
                    break
            
            if not categorized:
                uncategorized_vars.append((key, value))
        
        # 写入分类的配置
        for category, vars_list in categorized_vars.items():
            if vars_list:
                env_content.append(f"# {category}")
                env_content.append("# " + "=" * 39)
                for key, value in sorted(vars_list):
                    env_content.append(f"{key}={value}")
                env_content.append("")
        
        # 写入未分类的配置
        if uncategorized_vars:
            env_content.append("# 其他配置")
            env_content.append("# " + "=" * 39)
            for key, value in sorted(uncategorized_vars):
                env_content.append(f"{key}={value}")
        
        # 写入文件
        with open(self.env_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(env_content))
        
        print(f"✅ 配置已同步到: {self.env_file}")
        print(f"📦 原文件已备份到: {backup_file}")
        
        return True
    
    def compare_configs(self) -> Dict[str, any]:
        """比较Doppler和.env文件的配置"""
        env_vars = self.parse_env_file()
        doppler_vars = self.get_doppler_secrets() if self.check_doppler_available() else {}
        
        # 找出差异
        env_only = set(env_vars.keys()) - set(doppler_vars.keys())
        doppler_only = set(doppler_vars.keys()) - set(env_vars.keys())
        common = set(env_vars.keys()) & set(doppler_vars.keys())
        
        different_values = []
        for key in common:
            if env_vars[key] != doppler_vars[key]:
                different_values.append(key)
        
        return {
            "env_file_count": len(env_vars),
            "doppler_count": len(doppler_vars),
            "env_only": list(env_only),
            "doppler_only": list(doppler_only),
            "different_values": different_values,
            "identical": len(common) - len(different_values)
        }
    
    def print_comparison(self):
        """打印配置比较结果"""
        print("🔍 配置比较分析")
        print("=" * 40)
        
        comparison = self.compare_configs()
        
        print(f"📊 统计信息:")
        print(f"  .env文件: {comparison['env_file_count']} 个配置")
        print(f"  Doppler: {comparison['doppler_count']} 个配置")
        print(f"  相同配置: {comparison['identical']} 个")
        
        if comparison['env_only']:
            print(f"\n📁 仅在.env文件中存在 ({len(comparison['env_only'])} 个):")
            for key in comparison['env_only']:
                print(f"  - {key}")
        
        if comparison['doppler_only']:
            print(f"\n🔐 仅在Doppler中存在 ({len(comparison['doppler_only'])} 个):")
            for key in comparison['doppler_only']:
                print(f"  - {key}")
        
        if comparison['different_values']:
            print(f"\n⚠️ 值不同的配置 ({len(comparison['different_values'])} 个):")
            for key in comparison['different_values']:
                print(f"  - {key}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Cauldron配置同步工具")
    parser.add_argument("action", choices=["to-doppler", "from-doppler", "compare"], 
                       help="同步方向或比较")
    parser.add_argument("--force", action="store_true", help="强制执行，跳过确认")
    
    args = parser.parse_args()
    
    sync = ConfigSync()
    
    if args.action == "compare":
        sync.print_comparison()
    elif args.action == "to-doppler":
        if not args.force:
            confirm = input("确认将.env文件同步到Doppler? (y/N): ")
            if confirm.lower() not in ['y', 'yes']:
                print("❌ 同步已取消")
                return
        
        success = sync.sync_to_doppler()
        if success:
            print("✅ 同步到Doppler完成")
        else:
            print("❌ 同步到Doppler失败")
    
    elif args.action == "from-doppler":
        if not args.force:
            confirm = input("确认从Doppler同步到.env文件? 这将覆盖现有文件 (y/N): ")
            if confirm.lower() not in ['y', 'yes']:
                print("❌ 同步已取消")
                return
        
        success = sync.sync_from_doppler()
        if success:
            print("✅ 从Doppler同步完成")
        else:
            print("❌ 从Doppler同步失败")

if __name__ == "__main__":
    main()
