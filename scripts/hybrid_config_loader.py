#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合配置加载器
在Doppler不可用时，从.env文件加载配置，但保持Doppler优先的架构
"""

import os
import subprocess
from pathlib import Path
from dotenv import load_dotenv

class HybridConfigLoader:
    """混合配置加载器 - 支持Doppler和.env文件的无缝切换"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.doppler_available = self._check_doppler()
        self.config_loaded = False
        
    def _check_doppler(self) -> bool:
        """检查Doppler是否可用"""
        try:
            # 检查Doppler CLI
            result = subprocess.run(['doppler', '--version'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                return False
                
            # 检查项目配置
            result = subprocess.run(['doppler', 'configure', 'get'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                return False
                
            # 尝试列出密钥
            result = subprocess.run(['doppler', 'secrets', 'list'],
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
            
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def load_config(self):
        """加载配置"""
        if self.config_loaded:
            return
            
        if self.doppler_available:
            print("🔐 使用Doppler加载配置...")
            self._load_from_doppler()
        else:
            print("📁 Doppler不可用，使用.env文件加载配置...")
            self._load_from_env()
            
        self.config_loaded = True
    
    def _load_from_doppler(self):
        """从Doppler加载配置"""
        try:
            # Doppler会自动注入环境变量
            # 这里可以添加额外的验证逻辑
            pass
        except Exception as e:
            print(f"⚠️ Doppler加载失败: {e}")
            print("📁 回退到.env文件...")
            self._load_from_env()
    
    def _load_from_env(self):
        """从.env文件加载配置"""
        env_files = [
            self.project_root / ".env",
            self.project_root / ".env.backup",
            self.project_root / ".env.local"
        ]
        
        for env_file in env_files:
            if env_file.exists():
                print(f"📄 加载配置文件: {env_file}")
                load_dotenv(env_file)
                break
        else:
            print("❌ 未找到任何.env配置文件")
    
    def get_config_status(self) -> dict:
        """获取配置状态"""
        self.load_config()
        
        # 检查关键配置
        critical_vars = [
            "DATABASE_URL", "ZILLIZ_TOKEN", "OPENROUTER_API_KEY_1",
            "MASTODON_ACCESS_TOKEN", "ANTHROPIC_AUTH_TOKEN"
        ]
        
        status = {
            "doppler_available": self.doppler_available,
            "config_source": "Doppler" if self.doppler_available else ".env文件",
            "critical_vars": {},
            "total_vars": len(os.environ),
            "missing_vars": []
        }
        
        for var in critical_vars:
            value = os.getenv(var)
            if value:
                # 遮蔽敏感信息
                masked = f"{value[:8]}...{value[-4:]}" if len(value) > 12 else value[:6] + "..."
                status["critical_vars"][var] = masked
            else:
                status["missing_vars"].append(var)
        
        return status
    
    def print_status(self):
        """打印配置状态"""
        status = self.get_config_status()
        
        print("🔧 Cauldron项目配置状态")
        print("=" * 40)
        print(f"📊 配置源: {status['config_source']}")
        print(f"🔐 Doppler可用: {'是' if status['doppler_available'] else '否'}")
        print(f"📈 环境变量总数: {status['total_vars']}")
        
        print("\n🔑 关键配置:")
        for var, value in status["critical_vars"].items():
            print(f"  ✅ {var}: {value}")
        
        if status["missing_vars"]:
            print("\n❌ 缺失配置:")
            for var in status["missing_vars"]:
                print(f"  - {var}")
        
        if not status["missing_vars"]:
            print("\n🎉 所有关键配置都已加载！")
        
        return len(status["missing_vars"]) == 0

def create_env_wrapper():
    """创建环境变量包装器"""
    project_root = Path(__file__).parent.parent
    wrapper_file = project_root / "config" / "env_wrapper.py"
    
    # 确保config目录存在
    wrapper_file.parent.mkdir(exist_ok=True)
    
    wrapper_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境变量包装器
自动加载配置的便捷模块
"""

import os
from pathlib import Path

# 自动加载配置
def load_config():
    """自动加载配置"""
    try:
        from scripts.hybrid_config_loader import HybridConfigLoader
        loader = HybridConfigLoader()
        loader.load_config()
        return True
    except Exception as e:
        print(f"配置加载失败: {e}")
        return False

# 在模块导入时自动加载
_config_loaded = load_config()

def get_env(key: str, default: str = None) -> str:
    """获取环境变量的便捷函数"""
    return os.getenv(key, default)

def require_env(key: str) -> str:
    """获取必需的环境变量"""
    value = os.getenv(key)
    if not value:
        raise ValueError(f"必需的环境变量 {key} 未设置")
    return value

# 常用配置的快捷访问
DATABASE_URL = get_env("DATABASE_URL")
ZILLIZ_TOKEN = get_env("ZILLIZ_TOKEN")
OPENROUTER_API_KEY_1 = get_env("OPENROUTER_API_KEY_1")
MASTODON_ACCESS_TOKEN = get_env("MASTODON_ACCESS_TOKEN")
ANTHROPIC_AUTH_TOKEN = get_env("ANTHROPIC_AUTH_TOKEN")
'''
    
    with open(wrapper_file, 'w', encoding='utf-8') as f:
        f.write(wrapper_content)
    
    print(f"✅ 环境变量包装器已创建: {wrapper_file}")

def main():
    """主函数"""
    print("🔧 混合配置加载器测试")
    print("=" * 30)
    
    # 创建加载器
    loader = HybridConfigLoader()
    
    # 显示状态
    success = loader.print_status()
    
    # 创建包装器
    create_env_wrapper()
    
    print("\n🚀 使用方法:")
    print("1. 在Python代码中导入: from config.env_wrapper import *")
    print("2. 直接使用变量: DATABASE_URL, ZILLIZ_TOKEN 等")
    print("3. 或使用函数: get_env('YOUR_VAR'), require_env('REQUIRED_VAR')")
    
    return success

if __name__ == "__main__":
    main()
