#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
太公心易BI系统 - Streamlit Web应用
修复版本，确保在Heroku环境中正确运行
支持Doppler配置管理
"""

import os
import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

# 加载配置
try:
    from config.env_wrapper import get_env, require_env
    print("✅ 使用混合配置加载器")
except ImportError:
    print("⚠️ 回退到传统环境变量加载")
    from dotenv import load_dotenv
    load_dotenv()
    get_env = os.getenv
    def require_env(key):
        value = os.getenv(key)
        if not value:
            raise ValueError(f"必需的环境变量 {key} 未设置")
        return value

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 设置环境变量，避免Streamlit警告
os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
os.environ['STREAMLIT_SERVER_ENABLE_CORS'] = 'false'
os.environ['STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION'] = 'false'

# Heroku PostgreSQL数据库配置
database_url = get_env('DATABASE_URL')
if database_url:
    # 如果是postgres://开头，需要改为postgresql://
    if database_url.startswith('postgres://'):
        database_url = database_url.replace('postgres://', 'postgresql://', 1)
    os.environ['DATABASE_URL'] = database_url
    logger.info(f"✅ 检测到Heroku PostgreSQL数据库")
else:
    # 本地开发环境
    logger.info("🔧 使用本地开发环境配置")
    try:
        from src.core.config_manager import ConfigManager
        config = ConfigManager().get_cauldron_config()
        db_config = config['database']
        os.environ['DATABASE_URL'] = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['name']}"
    except ImportError as e:
        logger.warning(f"⚠️ 配置管理器导入失败: {e}，使用默认配置")
        # 使用.env文件中的配置或默认值
        db_user = os.getenv('DB_USER', 'ben')
        db_password = os.getenv('DB_PASSWORD', '')
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'cauldron')
        os.environ['DATABASE_URL'] = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

# 导入Streamlit和UI模块
import streamlit as st

# Streamlit页面配置
st.set_page_config(
    page_title="太公心易BI系统",
    page_icon="🏛️",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/ben/cauldron',
        'Report a bug': 'https://github.com/ben/cauldron/issues',
        'About': '太公心易BI系统 - 炼股葫芦 × 八仙论道'
    }
)

import subprocess

# 可选导入UI组件
try:
    from src.ui.morning_briefing_ui import MorningBriefingUI
    morning_briefing_available = True
except ImportError as e:
    logger.warning(f"⚠️ 晨会简报模块导入失败: {e}")
    MorningBriefingUI = None
    morning_briefing_available = False

try:
    from app.tabs.fengshen_tab import show_fengshen_tab
    fengshen_available = True
except ImportError as e:
    logger.warning(f"⚠️ 封神榜模块导入失败: {e}")
    show_fengshen_tab = None
    fengshen_available = False

# RSS Theater已在下面导入，删除重复导入

# 尝试导入稷下学宫
try:
    from src.ui.jixia_academy_ui import JixiaAcademyUI
    jixia_available = True
    logger.info("✅ 稷下学宫模块加载成功")
except ImportError as e:
    jixia_available = False
    logger.warning(f"⚠️ 稷下学宫模块导入失败: {e}")

# 尝试导入IB基本面数据（简化版）
try:
    # 暂时禁用IB模块以避免Streamlit事件循环问题
    # from src.ui.ib_simple_ui import IBSimpleUI
    ib_fundamentals_available = False
    logger.info("⚠️ IB基本面数据模块暂时禁用（事件循环问题）")
except ImportError as e:
    ib_fundamentals_available = False
    logger.warning(f"⚠️ IB基本面数据模块导入失败: {e}")

# 尝试导入七仙女雷达 (老版本，已废弃，使用新的七姐妹星团)
# try:
#     from src.ui.seven_fairies_ui import SevenFairiesUI
#     seven_fairies_available = True
#     logger.info("✅ 七仙女雷达模块加载成功")
# except ImportError as e:
seven_fairies_available = False
logger.info("ℹ️ 老版本七仙女雷达已废弃，使用新的七姐妹星团系统")

# 尝试导入真实API演示
try:
    import sys
    from pathlib import Path
    # 添加app目录到Python路径
    app_dir = Path(__file__).parent
    if str(app_dir) not in sys.path:
        sys.path.insert(0, str(app_dir))

    from tabs.real_api_tab import render_real_api_tab
    real_api_available = True
    logger.info("✅ 真实API演示模块加载成功")
except ImportError as e:
    real_api_available = False
    logger.warning(f"⚠️ 真实API演示模块导入失败: {e}")

# 尝试导入七仙女雷达
try:
    from tabs.seven_fairies_tab import render_seven_fairies_tab
    seven_fairies_radar_available = True
    logger.info("✅ 七仙女雷达模块加载成功")
except ImportError as e:
    seven_fairies_radar_available = False
    logger.warning(f"⚠️ 七仙女雷达模块导入失败: {e}")

# 尝试导入天下体系
try:
    from tabs.tianxia_tab import render_tianxia_tab
    tianxia_available = True
    logger.info("✅ 天下体系模块加载成功")
except ImportError as e:
    tianxia_available = False
    logger.warning(f"⚠️ 天下体系模块导入失败: {e}")

# RSS功能已迁移到N8N，相关UI模块已移除
rss_impact_available = False
rss_theater_available = False
logger.info("ℹ️ RSS功能已迁移到N8N平台，相关UI模块已移除")

# 尝试导入猴王修仙GameFi
try:
    from src.ui.monkey_king_gamefi_ui import render_monkey_king_gamefi
    gamefi_available = True
    logger.info("✅ 猴王修仙GameFi模块加载成功")
except ImportError as e:
    gamefi_available = False
    logger.warning(f"⚠️ 猴王修仙GameFi模块导入失败: {e}")

# 尝试导入DApp GameFi
try:
    from src.ui.dapp_gamefi_ui import render_dapp_gamefi
    dapp_gamefi_available = True
    logger.info("✅ DApp GameFi模块加载成功")
except ImportError as e:
    dapp_gamefi_available = False
    logger.warning(f"⚠️ DApp GameFi模块导入失败: {e}")

# 尝试导入十二长生系统
try:
    from src.ui.twelve_longevity_ui import render_twelve_longevity_ui
    twelve_longevity_available = True
    logger.info("✅ 十二长生系统模块加载成功")
except ImportError as e:
    twelve_longevity_available = False
    logger.warning(f"⚠️ 十二长生系统模块导入失败: {e}")

# 尝试导入完美十二境界系统
try:
    from src.ui.perfect_twelve_ui import render_perfect_twelve_ui
    perfect_twelve_available = True
    logger.info("✅ 完美十二境界系统模块加载成功")
except ImportError as e:
    perfect_twelve_available = False
    logger.warning(f"⚠️ 完美十二境界系统模块导入失败: {e}")

# 尝试导入函数射箭系统
try:
    from src.ui.function_arrow_ui import render_function_arrow_ui
    function_arrow_available = True
    logger.info("✅ 函数射箭系统模块加载成功")
except ImportError as e:
    function_arrow_available = False
    logger.warning(f"⚠️ 函数射箭系统模块导入失败: {e}")

# 尝试导入射幸合同系统
try:
    from src.ui.aleatory_contract_ui import render_aleatory_contract_ui
    aleatory_contract_available = True
    logger.info("✅ 射幸合同系统模块加载成功")
except ImportError as e:
    aleatory_contract_available = False
    logger.warning(f"⚠️ 射幸合同系统模块导入失败: {e}")

# 尝试导入大道生系统
try:
    from src.ui.dadaosheng_ui import render_dadaosheng_ui
    dadaosheng_available = True
    logger.info("✅ 大道生系统模块加载成功")
except ImportError as e:
    dadaosheng_available = False
    logger.warning(f"⚠️ 大道生系统模块导入失败: {e}")

# 尝试导入猴王修仙核心模块
try:
    from src.ui.monkey_king_module import render_monkey_king_module
    monkey_king_module_available = True
    logger.info("✅ 猴王修仙核心模块加载成功")
except ImportError as e:
    monkey_king_module_available = False
    logger.warning(f"⚠️ 猴王修仙核心模块导入失败: {e}")

def main():
    """主程序入口"""
    try:
        # 侧边栏导航
        with st.sidebar:
            st.title("🏛️ 太公心易BI")
            st.markdown("*颠覆传统交易思维，重塑投资认知边界*")

            # 会员等级显示
            st.markdown("---")
            st.subheader("🎯 太公三式")

            membership_levels = {
                "🆓 炼妖壶": "免费版 - RSS触发韭菜小剧场",
                "💎 降魔杵": "$39/月 - 六壬察心+遁甲择时",
                "👑 打神鞭": "$128/月 - 太乙观澜+射覆系统"
            }

            current_membership = st.selectbox(
                "当前会员等级",
                list(membership_levels.keys()),
                index=0
            )

            st.info(membership_levels[current_membership])

        # 创建主导航标签
        available_tabs = []
        
        # 添加可用的标签页
        # RSS冲击矩阵 - 放在首页位置
        if rss_impact_available:
            available_tabs.append("🌌 RSS冲击矩阵")
        
        # 猴王修仙核心模块
        if monkey_king_module_available:
            available_tabs.append("🐒 猴王修仙")
        
        # 其他功能模块
        if fengshen_available:
            available_tabs.append("🏆 封神榜")
        if seven_fairies_available:
            available_tabs.append("🧚‍♀️ 七仙女雷达")
        # 真实API演示
        if real_api_available:
            available_tabs.append("📈 真实API演示")
        # 七仙女雷达 (新版)
        if seven_fairies_radar_available:
            available_tabs.append("🌟 七仙女雷达")
        # 天下体系
        if tianxia_available:
            available_tabs.append("🏛️ 天下体系")
        # RSS功能已迁移到N8N，移除对韭当割标签页
        if jixia_available:
            available_tabs.append("🎭 稷下学宫")
        if ib_fundamentals_available:
            available_tabs.append("💎 六壬察心")
        
        # 晨会简报 - 放在最后，Freemium功能
        if morning_briefing_available:
            available_tabs.append("📈 晨会简报")
            
        # 如果没有可用的标签页，显示基本信息
        if not available_tabs:
            st.error("⚠️ 所有模块都无法加载，请检查依赖安装")
            st.info("请运行: pip install psycopg2-binary feedparser aiohttp")
            return

        tabs = st.tabs(available_tabs)
        tab_index = 0

        # RSS冲击矩阵功能已迁移到N8N，移除相关代码
        
        # 猴王修仙核心模块 - 整合所有修仙相关内容
        if monkey_king_module_available:
            with tabs[tab_index]:
                render_monkey_king_module()
            tab_index += 1

        # 封神榜
        if fengshen_available:
            with tabs[tab_index]:
                show_fengshen_tab()
            tab_index += 1
        
        # 七仙女雷达 (老版本已废弃)
        # if seven_fairies_available:
        #     with tabs[tab_index]:
        #         seven_fairies_ui = SevenFairiesUI()
        #         seven_fairies_ui.render()
        #     tab_index += 1

        # 真实API演示
        if real_api_available:
            with tabs[tab_index]:
                render_real_api_tab()
            tab_index += 1

        # 七仙女雷达 (新版)
        if seven_fairies_radar_available:
            with tabs[tab_index]:
                render_seven_fairies_tab()
            tab_index += 1

        # 天下体系
        if tianxia_available:
            with tabs[tab_index]:
                render_tianxia_tab()
            tab_index += 1

        # RSS韭菜小剧场功能已迁移到N8N，移除相关代码

        # 稷下学宫
        if jixia_available:
            with tabs[tab_index]:
                jixia_academy_ui = JixiaAcademyUI()
                jixia_academy_ui.run()
            tab_index += 1
        
        # 七仙女雷达 - 移动到前面
        # if seven_fairies_available:
        #     with tabs[tab_index]:
        #         seven_fairies_ui = SevenFairiesUI()
        #         seven_fairies_ui.render()
        #     tab_index += 1

        # IB基本面数据
        if ib_fundamentals_available:
            with tabs[tab_index]:
                st.markdown("### 💎 六壬察心 - IB基本面数据")
                st.markdown("**降魔杵专属功能** - 深度洞察市场情绪面，IB深度数据可视化")

                # 检查会员等级
                if current_membership == "🆓 炼妖壶":
                    st.warning("⚠️ 此功能需要降魔杵会员等级 ($39/月)")
                    st.info("💡 升级到降魔杵可解锁：六壬察心 + 遁甲择时功能")
                else:
                    st.info("💡 IB基本面数据功能暂时禁用（事件循环问题）")
                    st.info("🧪 请使用命令行版本: `python scripts/simple_ib_test.py`")
            tab_index += 1

        # 晨会简报 - 最后位置，Freemium功能
        if morning_briefing_available:
            with tabs[tab_index]:
                ui = MorningBriefingUI()
                ui.run()
            tab_index += 1

        # 添加页脚
        footer_text = f"<div style='text-align: center; color: grey;'>觅钥科技@2025</div>"
        st.markdown(footer_text, unsafe_allow_html=True)

        # 七仙女雷达 - 从这里移除，因为它已经被移到前面了

    except ImportError as e:
        st.error(f"导入错误: {e}")
        st.error("请确保所有依赖模块都已正确安装")
        logger.error(f"导入错误: {e}")
    except Exception as e:
        st.error(f"运行错误: {e}")
        logger.error(f"运行错误: {e}")

if __name__ == "__main__":
    main()