#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IB基本面数据UI组件
用于在Streamlit中展示IB基本面数据

功能：
- 股票基本面数据查询
- 多股票对比分析
- 数据可视化
- 导出功能

作者：太公心易BI系统
版本：v1.0
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import asyncio
from datetime import datetime
from typing import Dict, List, Optional

try:
    from src.data.ib_fundamentals_fetcher import IBFundamentalsFetcher, FundamentalData
    IB_AVAILABLE = True
except ImportError:
    IB_AVAILABLE = False


class IBFundamentalsUI:
    """IB基本面数据UI组件"""
    
    def __init__(self):
        self.fetcher = IBFundamentalsFetcher() if IB_AVAILABLE else None
        
    def render(self):
        """渲染UI界面"""
        st.markdown("### 💎 六壬察心 - IB基本面数据分析")
        st.markdown("*降魔杵专属功能 - 深度洞察市场情绪面*")
        
        if not IB_AVAILABLE:
            st.error("❌ IB基本面数据模块不可用，请检查依赖安装")
            st.code("pip install ib-insync")
            return
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs([
            "📊 单股分析", 
            "📈 多股对比", 
            "🎯 筛选器", 
            "📋 数据导出"
        ])
        
        with tab1:
            self._render_single_stock_analysis()
        
        with tab2:
            self._render_multi_stock_comparison()
        
        with tab3:
            self._render_stock_screener()
        
        with tab4:
            self._render_data_export()
    
    def _render_single_stock_analysis(self):
        """单股分析界面"""
        st.markdown("#### 📊 单股基本面分析")
        
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            symbol = st.text_input(
                "股票代码", 
                value="AAPL", 
                placeholder="输入股票代码，如 AAPL, MSFT"
            ).upper()
        
        with col2:
            exchange = st.selectbox(
                "交易所",
                ["SMART", "NYSE", "NASDAQ", "SEHK"],
                index=0
            )
        
        with col3:
            currency = st.selectbox(
                "货币",
                ["USD", "HKD", "CNY"],
                index=0
            )
        
        if st.button("🔍 获取基本面数据", type="primary"):
            with st.spinner(f"正在获取 {symbol} 的基本面数据..."):
                try:
                    # 运行异步函数
                    data = asyncio.run(
                        self.fetcher.get_stock_fundamentals(symbol, exchange, currency)
                    )
                    
                    if data:
                        self._display_fundamental_data(data)
                    else:
                        st.error(f"❌ 无法获取 {symbol} 的基本面数据")
                        
                except Exception as e:
                    st.error(f"❌ 获取数据时出错: {e}")
    
    def _display_fundamental_data(self, data: FundamentalData):
        """显示基本面数据"""
        st.success(f"✅ 成功获取 {data.symbol} 基本面数据")
        
        # 基本信息
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("公司名称", data.company_name)
            st.metric("行业", data.sector)
        
        with col2:
            if data.market_cap:
                market_cap_b = data.market_cap / 1e9
                st.metric("市值", f"${market_cap_b:.1f}B")
            else:
                st.metric("市值", "N/A")
            
            if data.pe_ratio:
                st.metric("PE比率", f"{data.pe_ratio:.2f}")
            else:
                st.metric("PE比率", "N/A")
        
        with col3:
            if data.pb_ratio:
                st.metric("PB比率", f"{data.pb_ratio:.2f}")
            else:
                st.metric("PB比率", "N/A")
            
            if data.roe:
                st.metric("ROE", f"{data.roe:.2f}%")
            else:
                st.metric("ROE", "N/A")
        
        with col4:
            if data.debt_to_equity:
                st.metric("负债权益比", f"{data.debt_to_equity:.2f}")
            else:
                st.metric("负债权益比", "N/A")
            
            if data.dividend_yield:
                st.metric("股息率", f"{data.dividend_yield:.2f}%")
            else:
                st.metric("股息率", "N/A")
        
        # 增长指标
        st.markdown("#### 📈 增长指标")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if data.revenue_growth:
                st.metric("营收增长", f"{data.revenue_growth:.2f}%")
            else:
                st.metric("营收增长", "N/A")
        
        with col2:
            if data.eps_growth:
                st.metric("EPS增长", f"{data.eps_growth:.2f}%")
            else:
                st.metric("EPS增长", "N/A")
        
        with col3:
            if data.analyst_rating:
                st.metric("分析师评级", data.analyst_rating)
            else:
                st.metric("分析师评级", "N/A")
        
        # 市场情绪指标
        st.markdown("#### 🎭 市场情绪指标")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if data.insider_ownership:
                st.metric("内部持股", f"{data.insider_ownership:.2f}%")
            else:
                st.metric("内部持股", "N/A")
        
        with col2:
            if data.short_interest:
                st.metric("空头比例", f"{data.short_interest:.2f}%")
            else:
                st.metric("空头比例", "N/A")
        
        with col3:
            if data.price_target:
                st.metric("目标价", f"${data.price_target:.2f}")
            else:
                st.metric("目标价", "N/A")
        
        # 数据更新时间
        st.caption(f"数据更新时间: {data.last_updated.strftime('%Y-%m-%d %H:%M:%S')}")
    
    def _render_multi_stock_comparison(self):
        """多股对比界面"""
        st.markdown("#### 📈 多股基本面对比")
        
        # 股票列表输入
        symbols_input = st.text_area(
            "股票代码列表",
            value="AAPL,MSFT,GOOGL,AMZN,TSLA",
            placeholder="输入多个股票代码，用逗号分隔",
            height=100,
            key="ib_fundamentals_symbols_input"
        )
        
        symbols = [s.strip().upper() for s in symbols_input.split(',') if s.strip()]
        
        if len(symbols) > 10:
            st.warning("⚠️ 最多支持10只股票对比")
            symbols = symbols[:10]
        
        if st.button("🔍 批量获取数据", type="primary"):
            if symbols:
                with st.spinner(f"正在获取 {len(symbols)} 只股票的基本面数据..."):
                    try:
                        # 运行异步函数
                        data_dict = asyncio.run(
                            self.fetcher.get_multiple_stocks_fundamentals(symbols)
                        )
                        
                        if data_dict:
                            self._display_comparison_data(data_dict)
                        else:
                            st.error("❌ 无法获取任何股票数据")
                            
                    except Exception as e:
                        st.error(f"❌ 获取数据时出错: {e}")
            else:
                st.warning("⚠️ 请输入至少一个股票代码")
    
    def _display_comparison_data(self, data_dict: Dict[str, FundamentalData]):
        """显示对比数据"""
        st.success(f"✅ 成功获取 {len(data_dict)} 只股票数据")
        
        # 转换为DataFrame
        df = self.fetcher.to_dataframe(data_dict)
        
        # 数据表格
        st.markdown("#### 📋 基本面数据对比表")
        
        # 选择显示的列
        display_columns = st.multiselect(
            "选择显示的指标",
            ['pe_ratio', 'pb_ratio', 'roe', 'debt_to_equity', 'revenue_growth', 
             'eps_growth', 'dividend_yield', 'market_cap'],
            default=['pe_ratio', 'pb_ratio', 'roe', 'market_cap']
        )
        
        if display_columns:
            display_df = df[['symbol', 'company_name'] + display_columns].copy()
            
            # 格式化数值
            for col in display_columns:
                if col == 'market_cap':
                    display_df[col] = display_df[col].apply(
                        lambda x: f"${x/1e9:.1f}B" if pd.notna(x) else "N/A"
                    )
                elif col in ['pe_ratio', 'pb_ratio', 'roe', 'debt_to_equity', 
                           'revenue_growth', 'eps_growth', 'dividend_yield']:
                    display_df[col] = display_df[col].apply(
                        lambda x: f"{x:.2f}" if pd.notna(x) else "N/A"
                    )
            
            st.dataframe(display_df, use_container_width=True)
        
        # 可视化图表
        if len(display_columns) > 0:
            self._create_comparison_charts(df, display_columns)
    
    def _create_comparison_charts(self, df: pd.DataFrame, columns: List[str]):
        """创建对比图表"""
        st.markdown("#### 📊 可视化对比")
        
        # 选择图表类型
        chart_type = st.selectbox(
            "图表类型",
            ["柱状图", "雷达图", "散点图"],
            index=0
        )
        
        if chart_type == "柱状图":
            # 选择指标
            metric = st.selectbox("选择指标", columns)
            
            if metric in df.columns:
                fig = px.bar(
                    df.dropna(subset=[metric]),
                    x='symbol',
                    y=metric,
                    title=f"{metric} 对比",
                    color=metric,
                    color_continuous_scale='viridis'
                )
                fig.update_layout(height=400)
                st.plotly_chart(fig, use_container_width=True)
        
        elif chart_type == "雷达图":
            # 雷达图需要标准化数据
            numeric_cols = [col for col in columns if col in df.columns]
            if len(numeric_cols) >= 3:
                self._create_radar_chart(df, numeric_cols)
            else:
                st.warning("⚠️ 雷达图需要至少3个数值指标")
        
        elif chart_type == "散点图":
            if len(columns) >= 2:
                col1, col2 = st.columns(2)
                with col1:
                    x_metric = st.selectbox("X轴指标", columns, index=0)
                with col2:
                    y_metric = st.selectbox("Y轴指标", columns, index=1)
                
                if x_metric != y_metric:
                    fig = px.scatter(
                        df.dropna(subset=[x_metric, y_metric]),
                        x=x_metric,
                        y=y_metric,
                        text='symbol',
                        title=f"{x_metric} vs {y_metric}",
                        hover_data=['company_name']
                    )
                    fig.update_traces(textposition="top center")
                    fig.update_layout(height=400)
                    st.plotly_chart(fig, use_container_width=True)
            else:
                st.warning("⚠️ 散点图需要至少2个指标")
    
    def _create_radar_chart(self, df: pd.DataFrame, columns: List[str]):
        """创建雷达图"""
        # 标准化数据 (0-1)
        df_normalized = df[columns].copy()
        for col in columns:
            if df_normalized[col].notna().any():
                min_val = df_normalized[col].min()
                max_val = df_normalized[col].max()
                if max_val != min_val:
                    df_normalized[col] = (df_normalized[col] - min_val) / (max_val - min_val)
        
        fig = go.Figure()
        
        for idx, row in df.iterrows():
            if pd.notna(row['symbol']):
                values = [df_normalized.loc[idx, col] for col in columns]
                values.append(values[0])  # 闭合雷达图
                
                fig.add_trace(go.Scatterpolar(
                    r=values,
                    theta=columns + [columns[0]],
                    fill='toself',
                    name=row['symbol']
                ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1]
                )),
            showlegend=True,
            title="基本面指标雷达图",
            height=500
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _render_stock_screener(self):
        """股票筛选器"""
        st.markdown("#### 🎯 股票筛选器")
        st.info("💡 此功能需要预先建立股票数据库，当前为演示版本")
        
        # 筛选条件
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**估值指标**")
            pe_min, pe_max = st.slider("PE比率范围", 0.0, 100.0, (0.0, 50.0))
            pb_min, pb_max = st.slider("PB比率范围", 0.0, 10.0, (0.0, 5.0))
        
        with col2:
            st.markdown("**增长指标**")
            revenue_growth_min = st.number_input("最低营收增长率 (%)", value=0.0)
            eps_growth_min = st.number_input("最低EPS增长率 (%)", value=0.0)
        
        if st.button("🔍 开始筛选"):
            st.info("📊 筛选功能开发中，敬请期待...")
    
    def _render_data_export(self):
        """数据导出界面"""
        st.markdown("#### 📋 数据导出")
        
        if 'comparison_data' in st.session_state:
            df = st.session_state.comparison_data
            
            # 导出格式选择
            export_format = st.selectbox(
                "导出格式",
                ["CSV", "Excel", "JSON"],
                index=0
            )
            
            if st.button("📥 导出数据"):
                if export_format == "CSV":
                    csv = df.to_csv(index=False)
                    st.download_button(
                        label="下载CSV文件",
                        data=csv,
                        file_name=f"fundamentals_{datetime.now().strftime('%Y%m%d')}.csv",
                        mime="text/csv"
                    )
                elif export_format == "Excel":
                    # Excel导出需要额外处理
                    st.info("Excel导出功能开发中...")
                elif export_format == "JSON":
                    json_data = df.to_json(orient='records', indent=2)
                    st.download_button(
                        label="下载JSON文件",
                        data=json_data,
                        file_name=f"fundamentals_{datetime.now().strftime('%Y%m%d')}.json",
                        mime="application/json"
                    )
        else:
            st.info("💡 请先在'多股对比'页面获取数据，然后返回此处导出")


# 使用示例
def main():
    """测试UI组件"""
    st.set_page_config(
        page_title="IB基本面数据分析",
        page_icon="💎",
        layout="wide"
    )
    
    ui = IBFundamentalsUI()
    ui.render()


if __name__ == "__main__":
    main()
