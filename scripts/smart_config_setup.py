#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能配置设置脚本
自动检测环境并配置最佳的配置加载方式
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
from typing import Dict, List, Tuple

class SmartConfigSetup:
    """智能配置设置"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.platform = platform.system().lower()
        self.is_heroku = 'DYNO' in os.environ
        self.is_docker = Path('/.dockerenv').exists()
        self.is_ci = any(ci_var in os.environ for ci_var in ['CI', 'GITHUB_ACTIONS', 'GITLAB_CI'])
        
    def detect_environment(self) -> Dict[str, any]:
        """检测运行环境"""
        env_info = {
            "platform": self.platform,
            "is_heroku": self.is_heroku,
            "is_docker": self.is_docker,
            "is_ci": self.is_ci,
            "python_version": sys.version,
            "has_dotenv": self._check_file_exists(".env"),
            "has_doppler_config": self._check_file_exists(".env.doppler"),
            "doppler_cli_installed": self._check_doppler_cli(),
            "doppler_configured": False,
            "recommended_mode": "env_file"
        }
        
        # 检查Doppler配置
        if env_info["doppler_cli_installed"]:
            env_info["doppler_configured"] = self._check_doppler_configured()
        
        # 推荐配置模式
        env_info["recommended_mode"] = self._recommend_config_mode(env_info)
        
        return env_info
    
    def _check_file_exists(self, filename: str) -> bool:
        """检查文件是否存在"""
        return (self.project_root / filename).exists()
    
    def _check_doppler_cli(self) -> bool:
        """检查Doppler CLI是否安装"""
        try:
            result = subprocess.run(['doppler', '--version'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def _check_doppler_configured(self) -> bool:
        """检查Doppler是否已配置"""
        try:
            result = subprocess.run(['doppler', 'configure', 'get'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def _recommend_config_mode(self, env_info: Dict) -> str:
        """推荐配置模式"""
        # Heroku环境
        if env_info["is_heroku"]:
            if env_info["doppler_configured"]:
                return "doppler"
            else:
                return "heroku_config"
        
        # CI环境
        if env_info["is_ci"]:
            return "env_file"
        
        # Docker环境
        if env_info["is_docker"]:
            if env_info["doppler_configured"]:
                return "doppler"
            else:
                return "env_file"
        
        # 本地开发环境
        if env_info["doppler_configured"]:
            return "doppler"
        elif env_info["has_dotenv"]:
            return "env_file"
        else:
            return "setup_required"
    
    def setup_for_environment(self, env_info: Dict) -> bool:
        """为当前环境设置配置"""
        mode = env_info["recommended_mode"]
        
        print(f"🎯 推荐配置模式: {mode}")
        
        if mode == "doppler":
            return self._setup_doppler_mode()
        elif mode == "heroku_config":
            return self._setup_heroku_mode()
        elif mode == "env_file":
            return self._setup_env_file_mode()
        elif mode == "setup_required":
            return self._setup_initial_config()
        
        return False
    
    def _setup_doppler_mode(self) -> bool:
        """设置Doppler模式"""
        print("🔐 配置Doppler模式...")
        
        # 设置环境变量
        os.environ['DOPPLER_ENABLED'] = 'true'
        
        # 创建配置文件
        doppler_env = self.project_root / ".env.doppler"
        if not doppler_env.exists():
            content = """# Doppler配置
DOPPLER_ENABLED=true
DOPPLER_PROJECT=cauldron
DOPPLER_CONFIG=development
"""
            with open(doppler_env, 'w') as f:
                f.write(content)
            print(f"✅ 创建Doppler配置文件: {doppler_env}")
        
        print("✅ Doppler模式配置完成")
        return True
    
    def _setup_heroku_mode(self) -> bool:
        """设置Heroku模式"""
        print("🚀 配置Heroku模式...")
        
        # 在Heroku环境中，优先使用Config Vars
        os.environ['USE_HEROKU_CONFIG'] = 'true'
        
        print("✅ Heroku模式配置完成")
        return True
    
    def _setup_env_file_mode(self) -> bool:
        """设置.env文件模式"""
        print("📁 配置.env文件模式...")
        
        env_file = self.project_root / ".env"
        if not env_file.exists():
            # 尝试从备份恢复
            backup_file = self.project_root / ".env.backup"
            if backup_file.exists():
                import shutil
                shutil.copy2(backup_file, env_file)
                print(f"✅ 从备份恢复.env文件")
            else:
                print("❌ 未找到.env文件或备份文件")
                return False
        
        # 禁用Doppler模式
        os.environ['DOPPLER_ENABLED'] = 'false'
        
        print("✅ .env文件模式配置完成")
        return True
    
    def _setup_initial_config(self) -> bool:
        """设置初始配置"""
        print("🔧 进行初始配置...")
        
        print("请选择配置方式:")
        print("1. 使用.env文件（推荐用于开发）")
        print("2. 安装并配置Doppler（推荐用于生产）")
        print("3. 跳过配置")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            return self._create_sample_env_file()
        elif choice == "2":
            return self._guide_doppler_setup()
        else:
            print("⚠️ 跳过配置，请手动设置环境变量")
            return True
    
    def _create_sample_env_file(self) -> bool:
        """创建示例.env文件"""
        env_file = self.project_root / ".env"
        
        sample_content = """# ===========================================
# Cauldron项目环境变量配置
# ===========================================

# 数据库配置
DATABASE_URL=your_database_url_here
ZILLIZ_TOKEN=your_zilliz_token_here

# AI服务
OPENROUTER_API_KEY_1=your_openrouter_key_here
ANTHROPIC_AUTH_TOKEN=your_anthropic_key_here

# 其他服务
MASTODON_ACCESS_TOKEN=your_mastodon_token_here
TAVILY_API_KEY=your_tavily_key_here

# 请替换上述占位符为实际的API密钥
"""
        
        with open(env_file, 'w') as f:
            f.write(sample_content)
        
        print(f"✅ 创建示例.env文件: {env_file}")
        print("⚠️ 请编辑.env文件，填入实际的API密钥")
        
        return True
    
    def _guide_doppler_setup(self) -> bool:
        """指导Doppler设置"""
        print("\n🔐 Doppler设置指南:")
        print("1. 安装Doppler CLI:")
        
        if self.platform == "darwin":
            print("   brew install dopplerhq/cli/doppler")
        else:
            print("   curl -Ls https://cli.doppler.com/install.sh | sh")
        
        print("\n2. 登录Doppler:")
        print("   doppler login")
        
        print("\n3. 配置项目:")
        print("   doppler setup --project cauldron --config development")
        
        print("\n4. 上传配置:")
        print("   python scripts/config_sync.py to-doppler")
        
        return True
    
    def print_environment_info(self, env_info: Dict):
        """打印环境信息"""
        print("🔍 环境检测结果")
        print("=" * 40)
        
        print(f"🖥️  平台: {env_info['platform']}")
        print(f"🐍 Python: {env_info['python_version'].split()[0]}")
        
        print(f"\n📍 运行环境:")
        print(f"  Heroku: {'是' if env_info['is_heroku'] else '否'}")
        print(f"  Docker: {'是' if env_info['is_docker'] else '否'}")
        print(f"  CI/CD: {'是' if env_info['is_ci'] else '否'}")
        
        print(f"\n📁 配置文件:")
        print(f"  .env文件: {'存在' if env_info['has_dotenv'] else '不存在'}")
        print(f"  Doppler配置: {'存在' if env_info['has_doppler_config'] else '不存在'}")
        
        print(f"\n🔐 Doppler状态:")
        print(f"  CLI安装: {'是' if env_info['doppler_cli_installed'] else '否'}")
        print(f"  项目配置: {'是' if env_info['doppler_configured'] else '否'}")
        
        print(f"\n🎯 推荐模式: {env_info['recommended_mode']}")

def main():
    """主函数"""
    print("🧠 Cauldron智能配置设置")
    print("=" * 40)
    
    setup = SmartConfigSetup()
    
    # 检测环境
    env_info = setup.detect_environment()
    setup.print_environment_info(env_info)
    
    print("\n" + "=" * 40)
    
    # 询问是否自动配置
    auto_setup = input("是否自动配置推荐的配置模式? (Y/n): ").strip().lower()
    
    if auto_setup in ['', 'y', 'yes']:
        success = setup.setup_for_environment(env_info)
        if success:
            print("\n✅ 配置设置完成！")
            
            # 提供下一步指导
            print("\n🚀 下一步:")
            if env_info["recommended_mode"] == "doppler":
                print("1. 运行: doppler run -- streamlit run app/streamlit_app.py")
                print("2. 或使用: ./scripts/run_streamlit_doppler.sh")
            else:
                print("1. 检查配置: python scripts/hybrid_config_loader.py")
                print("2. 运行应用: streamlit run app/streamlit_app.py")
        else:
            print("\n❌ 配置设置失败")
    else:
        print("\n⚠️ 跳过自动配置")
        print("请手动运行相应的配置脚本")

if __name__ == "__main__":
    main()
