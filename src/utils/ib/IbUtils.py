from ib_insync import Trade, Ticker
from pytz import timezone
from utils.LogUtils import logger


def trade_invalid(trade: Trade | None) -> bool:
    return trade is None or (trade.isDone() and trade.filled() == 0)


def trade_filled(trade: Trade | None) -> bool:
    return trade is not None and trade.isDone() and trade.filled() > 0


def ticker_price(ticker: Ticker, price_to_check: str = 'mid') -> float:
    if price_to_check == 'mid':
        return ticker.midpoint()
    if price_to_check == 'ask':
        return ticker.ask
    if price_to_check == 'bid':
        return ticker.bid
    if price_to_check == 'last':
        return ticker.last
    if price_to_check == 'close':
        return ticker.close
    return ticker.midpoint()


def log_trade_status_update(trade: Trade):
    if trade.orderStatus.status == "Filled":
        logger.info(f"Order filled!! filled Price = {trade.fills[0].execution.avgPrice}")
        logger.info("====this order logs:")
        for log in trade.log:
            logger.info(f"{log.time}#{log.status}({log.errorCode})#{log.message}")
    if trade.orderStatus.status == 'Cancelled' or trade.orderStatus.status == 'ApiCancelled':
        logger.info(f"Order Cancelled!! Message: {trade.advancedError}")
        logger.info("====this order logs:")
        for log in trade.log:
            logger.info(f"{log.time}#{log.status}({log.errorCode})#{log.message}")


def get_ticker_time_str(ticker: Ticker):
    time = ticker.time.astimezone(timezone('US/Eastern'))
    millisecond = time.strftime('%f')[:3]
    return f"{time.strftime('%H:%M:%S')}.{millisecond}"


def log_ticker_price(ticker: Ticker):
    logger.info(
        f"[{get_ticker_time_str(ticker)}]>>>"
        f"{ticker.contract.right}{ticker.contract.strike} updated: "
        f"ask={ticker.ask}, bid={ticker.bid}, mid={ticker.midpoint()}, last={ticker.last}")
