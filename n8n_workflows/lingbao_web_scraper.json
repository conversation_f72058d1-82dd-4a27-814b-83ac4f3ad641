{"name": "🔮 灵宝道君Web爬虫验证系统", "nodes": [{"parameters": {"httpMethod": "POST", "path": "lingbao-verify", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "灵宝道君验证触发器", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// 解析验证请求\nconst requestData = $input.first().json;\n\n// 提取要验证的论断\nconst claims = requestData.claims || [];\nconst searchQueries = [];\n\n// 为每个论断生成搜索查询\nclaims.forEach((claim, index) => {\n  // 提取关键词\n  let keywords = claim.replace(/[，。！？]/g, ' ').split(' ').filter(word => word.length > 1);\n  \n  // 生成多个搜索查询\n  searchQueries.push({\n    id: `claim_${index + 1}`,\n    original_claim: claim,\n    queries: [\n      `${keywords.slice(0, 3).join(' ')} 最新消息`,\n      `${keywords.slice(0, 2).join(' ')} 2024 2025`,\n      `${keywords[0]} 分析 预测`,\n      `${keywords.slice(0, 2).join(' ')} 财报 数据`\n    ]\n  });\n});\n\nreturn searchQueries.map(sq => ({ json: sq }));"}, "id": "query-generator", "name": "查询生成器", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "https://www.google.com/search", "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "={{ $json.queries[0] }}"}, {"name": "num", "value": "10"}, {"name": "hl", "value": "zh-CN"}]}, "options": {"timeout": 10000, "redirect": {"redirect": {}}}}, "id": "google-search", "name": "Google搜索", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"jsCode": "// 解析Google搜索结果\nconst html = $input.first().json.data;\nconst cheerio = require('cheerio');\nconst $ = cheerio.load(html);\n\nconst results = [];\n\n// 提取搜索结果\n$('div.g').each((i, element) => {\n  const title = $(element).find('h3').text();\n  const link = $(element).find('a').attr('href');\n  const snippet = $(element).find('.VwiC3b').text();\n  \n  if (title && link && snippet) {\n    results.push({\n      title: title,\n      url: link,\n      snippet: snippet,\n      relevance_score: snippet.length > 50 ? 1 : 0.5\n    });\n  }\n});\n\n// 返回前5个最相关的结果\nconst topResults = results.slice(0, 5);\n\nreturn [{\n  json: {\n    claim_id: $input.first().json.id,\n    original_claim: $input.first().json.original_claim,\n    search_query: $input.first().json.queries[0],\n    results: topResults,\n    result_count: topResults.length,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "result-parser", "name": "结果解析器", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"url": "={{ $json.results[0].url }}", "options": {"timeout": 8000}}, "id": "content-fetcher", "name": "内容抓取器", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300]}, {"parameters": {"jsCode": "// 内容分析和置信度计算\nconst searchData = $input.first().json;\nconst contentData = $input.last().json.data || '';\n\n// 简单的关键词匹配分析\nfunction analyzeContent(claim, content) {\n  const claimWords = claim.toLowerCase().split(' ');\n  const contentLower = content.toLowerCase();\n  \n  let matchCount = 0;\n  let supportingEvidence = [];\n  let contradictingEvidence = [];\n  \n  // 检查关键词匹配\n  claimWords.forEach(word => {\n    if (word.length > 2 && contentLower.includes(word)) {\n      matchCount++;\n    }\n  });\n  \n  // 查找支持性证据\n  const supportWords = ['增长', '上涨', '超预期', '强劲', '看好', '乐观'];\n  const contradictWords = ['下跌', '下降', '悲观', '风险', '担忧', '下调'];\n  \n  supportWords.forEach(word => {\n    if (contentLower.includes(word)) {\n      supportingEvidence.push(`发现支持性词汇: ${word}`);\n    }\n  });\n  \n  contradictWords.forEach(word => {\n    if (contentLower.includes(word)) {\n      contradictingEvidence.push(`发现反对性词汇: ${word}`);\n    }\n  });\n  \n  // 计算置信度\n  let confidence = 0.5; // 基础置信度\n  confidence += (matchCount * 0.1); // 关键词匹配加分\n  confidence += (supportingEvidence.length * 0.1); // 支持证据加分\n  confidence -= (contradictingEvidence.length * 0.1); // 反对证据减分\n  \n  confidence = Math.max(0, Math.min(1, confidence)); // 限制在0-1之间\n  \n  return {\n    confidence: confidence,\n    supporting_evidence: supportingEvidence,\n    contradicting_evidence: contradictingEvidence,\n    keyword_matches: matchCount\n  };\n}\n\n// 分析结果\nconst analysis = analyzeContent(searchData.original_claim, contentData);\n\nreturn [{\n  json: {\n    verification_id: `verify_${Date.now()}`,\n    claim: searchData.original_claim,\n    search_results: searchData.results,\n    content_analysis: analysis,\n    confidence_score: analysis.confidence,\n    evidence_summary: {\n      supporting: analysis.supporting_evidence,\n      contradicting: analysis.contradicting_evidence\n    },\n    recommendation: analysis.confidence >= 0.7 ? 'APPROVE' : \n                   analysis.confidence >= 0.5 ? 'REVIEW' : 'REJECT',\n    timestamp: new Date().toISOString(),\n    data_sources: searchData.results.map(r => r.url)\n  }\n}];"}, "id": "confidence-calculator", "name": "置信度计算器", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "response-node", "name": "验证结果返回", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"url": "https://cn.investing.com/search/?q={{ encodeURIComponent($json.queries[1]) }}", "options": {"timeout": 8000}}, "id": "investing-search", "name": "Investing.com搜索", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 480]}, {"parameters": {"url": "https://finance.sina.com.cn/search/?q={{ encodeURIComponent($json.queries[2]) }}", "options": {"timeout": 8000}}, "id": "sina-search", "name": "新浪财经搜索", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 660]}], "connections": {"webhook-trigger": {"main": [[{"node": "query-generator", "type": "main", "index": 0}]]}, "query-generator": {"main": [[{"node": "google-search", "type": "main", "index": 0}, {"node": "investing-search", "type": "main", "index": 0}, {"node": "sina-search", "type": "main", "index": 0}]]}, "google-search": {"main": [[{"node": "result-parser", "type": "main", "index": 0}]]}, "result-parser": {"main": [[{"node": "content-fetcher", "type": "main", "index": 0}]]}, "content-fetcher": {"main": [[{"node": "confidence-calculator", "type": "main", "index": 0}]]}, "confidence-calculator": {"main": [[{"node": "response-node", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-13T08:30:00.000Z", "versionId": "1"}