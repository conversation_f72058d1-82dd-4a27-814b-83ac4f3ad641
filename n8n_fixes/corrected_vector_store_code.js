// 修正后的N8N代码 - 适配Milvus Vector Store节点
const processedItems = [];
const items = $input.all();

for (const item of items) {
    if (item.json.title && item.json.title.trim() !== '') {
        
        const publishedDate = new Date(item.json.published_time);
        const simpleTime = publishedDate.toISOString();

        // 确保所有元数据值都是字符串
        const safeMetadata = {
            title: String(item.json.title),
            published_date: String(simpleTime),
            article_id: String(item.json.article_id),
            source: String(item.json.source || 'unknown'),
            url: String(item.json.url || '')
        };

        // Vector Store节点期望的LangChain Document格式
        const formattedItem = {
            pageContent: String(item.json.content || item.json.title), // 使用实际内容，而不是标题
            metadata: safeMetadata
        };
        
        // 直接推送文档，不要包装在json对象中
        processedItems.push(formattedItem);
    }
}

if (processedItems.length === 0) {
    return [{ error: "No valid items to process." }];
}

// 返回文档数组，Vector Store节点会自动处理
return processedItems;
