#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理仪表板
Web界面管理环境变量和Doppler配置
"""

import streamlit as st
import os
import json
from pathlib import Path
import subprocess
from datetime import datetime

# 加载配置
try:
    from config.env_wrapper import get_env
    from scripts.hybrid_config_loader import HybridConfigLoader
except ImportError:
    st.error("配置加载器未找到，请确保已完成Doppler迁移")
    st.stop()

def check_doppler_status():
    """检查Doppler状态"""
    try:
        result = subprocess.run(['doppler', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            # 检查项目配置
            result = subprocess.run(['doppler', 'configure', 'get'],
                                  capture_output=True, text=True, timeout=5)
            return {
                "installed": True,
                "configured": result.returncode == 0,
                "version": result.stdout.strip() if result.returncode == 0 else "未知"
            }
    except:
        pass
    
    return {"installed": False, "configured": False, "version": None}

def get_config_categories():
    """获取配置分类"""
    return {
        "🗄️ 数据库": [
            "DATABASE_URL", "ZILLIZ_ENDPOINT", "ZILLIZ_TOKEN", "ZILLIZ_USER", "ZILLIZ_PASSWD",
            "MONGO_API_PUBLIC_KEY", "MONGO_API_PRIVATE_KEY", "MILVUS_URI", "MILVUS_TOKEN"
        ],
        "🤖 AI服务": [
            "OPENROUTER_API_KEY_1", "OPENROUTER_API_KEY_2", "OPENROUTER_API_KEY_3", "OPENROUTER_API_KEY_4",
            "ANTHROPIC_AUTH_TOKEN", "GEMINI_API_KEY", "HF_TOKEN"
        ],
        "💰 金融API": [
            "finnhub", "coingecko", "coindesk", "blockchair", "ALPHA_VANTAGE_API", "RAPIDAPI_KEY",
            "coinbase_id", "coinbase_secret"
        ],
        "🔗 集成服务": [
            "TAVILY_API_KEY", "FIRECRAWL_API", "n8n_token", "N8N_RSS_FLOW", "MASTODON_ACCESS_TOKEN"
        ],
        "☁️ 云服务": [
            "SUPABASE_URL", "SUPABASE_ANON_KEY", "UPSTASH_REDIS_URL", "QSTASH_TOKEN"
        ],
        "🏗️ 基础设施": [
            "IB_HOST", "IB_PORT", "IB_CLIENT_ID", "HEROKU_INFERENCE_URL", "DOPPLER_TOKEN"
        ]
    }

def mask_sensitive_value(value, show_length=8):
    """遮蔽敏感信息"""
    if not value:
        return "未设置"
    
    if len(value) <= show_length:
        return "*" * len(value)
    
    return f"{value[:4]}...{value[-4:]}"

def main():
    """主界面"""
    st.set_page_config(
        page_title="Cauldron配置管理",
        page_icon="🔧",
        layout="wide"
    )
    
    st.title("🔧 Cauldron项目配置管理仪表板")
    st.markdown("---")
    
    # 侧边栏
    with st.sidebar:
        st.header("🎛️ 控制面板")
        
        # Doppler状态
        doppler_status = check_doppler_status()
        st.subheader("🔐 Doppler状态")
        
        if doppler_status["installed"]:
            st.success("✅ Doppler CLI已安装")
            if doppler_status["configured"]:
                st.success("✅ 项目已配置")
            else:
                st.warning("⚠️ 项目未配置")
        else:
            st.error("❌ Doppler CLI未安装")
        
        # 配置加载器状态
        st.subheader("📊 配置状态")
        loader = HybridConfigLoader()
        status = loader.get_config_status()
        
        st.info(f"📁 配置源: {status['config_source']}")
        st.info(f"📈 环境变量: {status['total_vars']} 个")
        
        if status['missing_vars']:
            st.error(f"❌ 缺失: {len(status['missing_vars'])} 个")
        else:
            st.success("✅ 关键配置完整")
    
    # 主内容区域
    tab1, tab2, tab3, tab4 = st.tabs(["📊 配置概览", "🔍 详细配置", "🔧 管理工具", "📖 使用指南"])
    
    with tab1:
        st.header("📊 配置概览")
        
        # 配置统计
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总环境变量", status['total_vars'])
        
        with col2:
            st.metric("关键配置", len(status['critical_vars']))
        
        with col3:
            st.metric("缺失配置", len(status['missing_vars']))
        
        with col4:
            doppler_enabled = os.getenv('DOPPLER_ENABLED', 'false').lower() == 'true'
            st.metric("Doppler模式", "启用" if doppler_enabled else "禁用")
        
        # 配置分类显示
        st.subheader("📋 配置分类状态")
        categories = get_config_categories()
        
        for category, vars_list in categories.items():
            with st.expander(f"{category} ({len(vars_list)} 项)"):
                for var in vars_list:
                    value = get_env(var)
                    if value:
                        st.success(f"✅ {var}: {mask_sensitive_value(value)}")
                    else:
                        st.error(f"❌ {var}: 未设置")
    
    with tab2:
        st.header("🔍 详细配置")
        
        # 搜索功能
        search_term = st.text_input("🔍 搜索配置项", placeholder="输入配置名称...")
        
        # 显示所有环境变量
        all_vars = dict(os.environ)
        
        if search_term:
            filtered_vars = {k: v for k, v in all_vars.items() 
                           if search_term.lower() in k.lower()}
        else:
            filtered_vars = all_vars
        
        st.subheader(f"📋 环境变量 ({len(filtered_vars)} 项)")
        
        # 分页显示
        items_per_page = 20
        total_pages = (len(filtered_vars) + items_per_page - 1) // items_per_page
        
        if total_pages > 1:
            page = st.selectbox("选择页面", range(1, total_pages + 1))
            start_idx = (page - 1) * items_per_page
            end_idx = start_idx + items_per_page
            page_vars = dict(list(filtered_vars.items())[start_idx:end_idx])
        else:
            page_vars = filtered_vars
        
        for key, value in page_vars.items():
            with st.expander(f"🔑 {key}"):
                col1, col2 = st.columns([1, 3])
                with col1:
                    st.write("**变量名:**")
                    st.code(key)
                with col2:
                    st.write("**值:**")
                    if any(sensitive in key.lower() for sensitive in ['key', 'token', 'secret', 'password', 'passwd']):
                        if st.checkbox(f"显示 {key}", key=f"show_{key}"):
                            st.code(value)
                        else:
                            st.code(mask_sensitive_value(value))
                    else:
                        st.code(value)
    
    with tab3:
        st.header("🔧 管理工具")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🔄 配置操作")
            
            if st.button("🔄 重新加载配置"):
                loader = HybridConfigLoader()
                loader.config_loaded = False
                loader.load_config()
                st.success("配置已重新加载")
                st.experimental_rerun()
            
            if st.button("📊 检查配置状态"):
                with st.spinner("检查中..."):
                    status = loader.get_config_status()
                    st.json(status)
            
            if st.button("💾 导出配置"):
                config_data = {k: mask_sensitive_value(v) for k, v in os.environ.items()}
                st.download_button(
                    "下载配置文件",
                    json.dumps(config_data, indent=2, ensure_ascii=False),
                    file_name=f"cauldron_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )
        
        with col2:
            st.subheader("🔐 Doppler操作")
            
            if doppler_status["installed"]:
                if st.button("🔧 配置Doppler项目"):
                    with st.spinner("配置中..."):
                        try:
                            result = subprocess.run(['doppler', 'setup', '--project', 'cauldron', '--config', 'development'],
                                                  capture_output=True, text=True, timeout=30)
                            if result.returncode == 0:
                                st.success("Doppler项目配置成功")
                            else:
                                st.error(f"配置失败: {result.stderr}")
                        except Exception as e:
                            st.error(f"配置失败: {e}")
                
                if st.button("📋 列出Doppler密钥"):
                    with st.spinner("获取中..."):
                        try:
                            result = subprocess.run(['doppler', 'secrets', 'list'],
                                                  capture_output=True, text=True, timeout=10)
                            if result.returncode == 0:
                                st.code(result.stdout)
                            else:
                                st.error(f"获取失败: {result.stderr}")
                        except Exception as e:
                            st.error(f"获取失败: {e}")
            else:
                st.info("请先安装Doppler CLI")
                st.code("curl -Ls https://cli.doppler.com/install.sh | sh")
    
    with tab4:
        st.header("📖 使用指南")
        
        st.markdown("""
        ### 🚀 快速开始
        
        1. **检查配置状态**
           ```bash
           python scripts/hybrid_config_loader.py
           ```
        
        2. **启用Doppler模式**
           ```bash
           export DOPPLER_ENABLED=true
           ```
        
        3. **运行应用**
           ```bash
           ./scripts/run_streamlit_doppler.sh
           ```
        
        ### 🔧 配置管理
        
        #### Python代码中使用
        ```python
        from config.env_wrapper import get_env, require_env
        
        # 获取配置
        database_url = get_env('DATABASE_URL')
        api_key = require_env('OPENROUTER_API_KEY_1')
        ```
        
        #### 环境变量设置
        ```bash
        # 临时设置
        export DOPPLER_ENABLED=true
        
        # 永久设置（添加到 ~/.bashrc 或 ~/.zshrc）
        echo 'export DOPPLER_ENABLED=true' >> ~/.bashrc
        ```
        
        ### 🔐 Doppler配置
        
        1. **安装CLI**
           ```bash
           curl -Ls https://cli.doppler.com/install.sh | sh
           ```
        
        2. **登录和配置**
           ```bash
           doppler login
           doppler setup --project cauldron --config development
           ```
        
        3. **上传密钥**
           ```bash
           python scripts/migrate_to_doppler.py
           ```
        
        ### ⚠️ 故障排除
        
        - **Doppler不可用**: 系统自动回退到.env文件
        - **配置缺失**: 检查.env.backup文件
        - **权限问题**: 确保脚本有执行权限
        """)

if __name__ == "__main__":
    main()
