from typing import List, Dict, Generic, TypeVar, Optional
from pydantic import BaseModel, Field

# 定义一个泛型类型T，它必须是BaseMythicalBeing的子类
T = TypeVar('T', bound='BaseMythicalBeing')

class BaseMythicalBeing(BaseModel):
    """
    所有神话生物的基类，强制要求必须有一个'name'属性。
    这是我们神话体系的“原子单位”。
    """
    name: str = Field(..., description="神话生物的唯一名称。")

class MythologyEngine(Generic[T]):
    """
    一个可配置的、通用的“神话引擎”。
    它像一个神殿，可以管理任何自定义的神话生物集合（如龙子、希腊众神等）。
    """
    def __init__(self, name: str, beings: List[T]):
        self.name = name
        self._beings_list: List[T] = beings
        self._beings_map: Dict[str, T] = {being.name: being for being in beings}
        print(f"🏛️ Mythology Engine '{self.name}' initialized with {len(self._beings_list)} beings.")

    def get_all(self) -> List[T]:
        """返回所有神话生物的列表。"""
        return self._beings_list

    def get_by_name(self, name: str) -> Optional[T]:
        """通过名称高效查找神话生物。"""
        return self._beings_map.get(name)