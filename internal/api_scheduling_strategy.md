# 🧠 Yahoo Finance API智能调度策略

## 核心问题：什么时候调用什么API？

基于你的6个Yahoo Finance API订阅，我们设计了一套智能调度策略，确保在正确的时间使用正确的API。

## 🎯 API专业化分工

### 1. **Yahoo Finance 经典版** (yahoo-finance15.p.rapidapi.com)
```
🏛️ 专长：全面基础功能
📊 最佳用途：
  - 实时股票报价 (/api/yahoo/qu/quote/{symbol})
  - 涨跌幅榜单 (/api/yahoo/co/collections/day_gainers)
  - 市场新闻 (/api/yahoo/ne/news)
  - 最活跃股票 (/api/yahoo/co/collections/most_actives)

⏰ 最佳时机：
  - 交易时段 (9:30-16:00 EST) - 实时数据需求高
  - 需要综合市场概览时
  - 其他API达到限制时的备用选择
```

### 2. **YH Finance 完整版** (yh-finance.p.rapidapi.com)
```
🔬 专长：结构化深度数据
📊 最佳用途：
  - 公司档案 (/stock/v2/get-profile)
  - 股票摘要 (/stock/v2/get-summary)
  - 批量市场报价 (/market/v2/get-quotes)

⏰ 最佳时机：
  - 需要详细公司信息时
  - 进行基本面分析时
  - 批量处理多只股票时
  - 非交易时段的深度研究
```

### 3. **Yahoo Finance 搜索版** (yahoo-finance-api1.p.rapidapi.com)
```
🔍 专长：搜索和趋势发现
📊 最佳用途：
  - 股票搜索 (/v1/finance/search)
  - 趋势股票 (/v1/finance/trending)

⏰ 最佳时机：
  - 用户主动搜索股票时
  - 发现热门趋势时
  - 构建股票池时
  - 市场开盘前的准备阶段
```

### 4. **Yahoo Finance 实时版** (yahoo-finance-low-latency.p.rapidapi.com)
```
⚡ 专长：低延迟实时数据
📊 最佳用途：
  - 实时报价摘要 (/v11/finance/quoteSummary/{symbol})
  - 市场筛选器 (/ws/screeners/v1/finance/screener/predefined/saved)

⏰ 最佳时机：
  - 交易时段的高频更新
  - 需要最低延迟的场景
  - 实时监控和告警
  - 日内交易决策支持
```

### 5. **YH Finance 增强版** (yh-finance-complete.p.rapidapi.com)
```
📊 专长：历史深度数据
📊 最佳用途：
  - 股票详细信息 (/stock/get-detail)
  - 历史价格数据 (/stock/get-histories)

⏰ 最佳时机：
  - 技术分析需求
  - 回测策略时
  - 夜间数据处理
  - 生成历史报告
```

### 6. **Yahoo Finance 基础版** (yahoo-finance127.p.rapidapi.com)
```
⚡ 专长：简洁高效
📊 最佳用途：
  - 简单价格查询 (/price/{symbol})
  - 关键统计数据 (/key-statistics/{symbol})

⏰ 最佳时机：
  - 高频简单查询
  - 系统健康检查
  - 其他API故障时的备用
  - 成本敏感的场景
```

## 🕐 时间窗口调度策略

### 交易时段 (9:30-16:00 EST)
```
🎯 优先级：实时性 > 成本
📊 主力API：
  1. Yahoo Finance 实时版 - 低延迟报价
  2. Yahoo Finance 经典版 - 综合数据
  3. Yahoo Finance 基础版 - 高频查询

🔄 轮换策略：
  - 每5分钟轮换主力API
  - 监控响应时间，超过200ms自动切换
  - 使用率超过80%时强制切换
```

### 盘前时段 (4:00-9:30 EST)
```
🎯 优先级：数据准备 > 实时性
📊 主力API：
  1. YH Finance 完整版 - 公司基本面
  2. Yahoo Finance 搜索版 - 趋势发现
  3. YH Finance 增强版 - 历史数据

🔄 轮换策略：
  - 批量处理优先
  - 为交易时段预热数据
  - 更新股票池和关注列表
```

### 盘后时段 (16:00-20:00 EST)
```
🎯 优先级：分析总结 > 实时性
📊 主力API：
  1. YH Finance 增强版 - 历史分析
  2. Yahoo Finance 经典版 - 新闻总结
  3. YH Finance 完整版 - 深度分析

🔄 轮换策略：
  - 生成日报和总结
  - 技术指标计算
  - 策略回测和优化
```

### 夜间时段 (20:00-4:00 EST)
```
🎯 优先级：成本效益 > 实时性
📊 主力API：
  1. Yahoo Finance 基础版 - 低成本维护
  2. YH Finance 增强版 - 历史数据处理
  3. Yahoo Finance 搜索版 - 趋势分析

🔄 轮换策略：
  - 最小化API调用
  - 数据清理和整理
  - 系统维护和备份
```

## 🎮 智能调度算法

### 评分机制 (总分100分)
```python
API评分 = (
    专长匹配度 * 40% +      # 是否擅长处理该类型数据
    使用率健康度 * 25% +    # 当前使用率是否合理
    性能质量 * 20% +        # 历史成功率和响应时间
    可靠性 * 10% +          # API稳定性
    成本效益 * 5%           # 调用成本
)
```

### 故障转移策略
```
1. 主API失败 → 自动切换到备用API
2. 连续3次失败 → 暂时屏蔽该API (30分钟)
3. 响应时间>500ms → 降级到更快的API
4. 使用率>90% → 强制切换到其他API
5. 所有API失败 → 启用紧急模式，使用缓存数据
```

### 负载均衡
```
1. 轮询策略：按使用率轮换API
2. 加权策略：根据API性能分配权重
3. 随机策略：20%概率选择次优API，避免过度集中
4. 时间策略：根据时间窗口调整优先级
```

## 🚀 实际应用场景

### 场景1：用户查询AAPL股价
```
1. 检测当前时间窗口 → 交易时段
2. 数据类型 → 实时报价
3. 智能选择 → Yahoo Finance 实时版 (评分最高)
4. 执行调用 → 成功，响应时间150ms
5. 更新统计 → 使用率+1，质量评分更新
```

### 场景2：批量获取50只股票数据
```
1. 检测数据类型 → 批量报价
2. 智能选择 → YH Finance 完整版 (专长匹配)
3. 分批处理 → 每批10只，避免单次过载
4. 故障转移 → 如失败，自动切换到Yahoo Finance 经典版
5. 负载均衡 → 后续批次使用不同API
```

### 场景3：夜间历史数据分析
```
1. 检测时间窗口 → 夜间时段
2. 数据类型 → 历史数据
3. 智能选择 → YH Finance 增强版 (专长+时间匹配)
4. 成本优化 → 优先使用低成本API
5. 批量处理 → 大批量数据处理
```

## 📊 监控和优化

### 实时监控指标
- API响应时间分布
- 成功率趋势
- 使用率分布
- 成本统计
- 故障转移频率

### 自动优化
- 每小时重置使用计数器
- 每日更新API性能评分
- 每周分析调度效果
- 每月优化调度策略

---

**总结：通过这套智能调度策略，你的"永动机"不仅能避免API限制，还能在正确的时间使用最合适的API，实现成本最优、性能最佳的数据获取！** 🎯
