# GraphRAG Server Dockerfile
FROM python:3.11-slim

LABEL maintainer="稷下学宫 <<EMAIL>>"
LABEL description="GraphRAG Server for 稷下学宫 AI辩论系统"

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建数据目录
RUN mkdir -p /app/data /app/config /app/logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV GRAPHRAG_DATA_DIR=/app/data
ENV GRAPHRAG_CONFIG_DIR=/app/config
ENV GRAPHRAG_LOG_LEVEL=INFO

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["python", "server.py"]
