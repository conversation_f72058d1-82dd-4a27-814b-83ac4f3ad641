#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GraphRAG Server for 稷下学宫
提供REST API接口，支持知识图谱构建和查询
"""

import os
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# GraphRAG imports
from graphrag.query.llm.oai.chat_openai import ChatOpenAI
from graphrag.query.llm.oai.embedding import OpenAIEmbedding
from graphrag.query.indexer_adapters import read_indexer_entities, read_indexer_reports
from graphrag.query.structured_search.global_search.community_context import GlobalCommunityContext
from graphrag.query.structured_search.global_search.search import GlobalSearch
from graphrag.query.structured_search.local_search.mixed_context import LocalSearchMixedContext
from graphrag.query.structured_search.local_search.search import LocalSearch

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# FastAPI应用
app = FastAPI(
    title="稷下学宫 GraphRAG Server",
    description="为稷下学宫AI辩论系统提供知识图谱服务",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型
class IndexRequest(BaseModel):
    """索引构建请求"""
    documents: List[str]
    source: str = "rss"
    rebuild: bool = False

class QueryRequest(BaseModel):
    """查询请求"""
    question: str
    method: str = "global"  # global, local, hybrid
    max_tokens: int = 8000
    context: Optional[Dict[str, Any]] = None

class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: str
    version: str
    services: Dict[str, str]

# GraphRAG服务类
class GraphRAGService:
    """GraphRAG服务"""
    
    def __init__(self):
        self.data_dir = Path(os.getenv("GRAPHRAG_DATA_DIR", "/app/data"))
        self.config_dir = Path(os.getenv("GRAPHRAG_CONFIG_DIR", "/app/config"))
        self.llm = None
        self.embedding = None
        self.global_search = None
        self.local_search = None
        self.initialized = False
        
    async def initialize(self):
        """初始化GraphRAG服务"""
        try:
            logger.info("🚀 初始化GraphRAG服务...")
            
            # 配置LLM
            self.llm = ChatOpenAI(
                api_key=os.getenv("OPENAI_API_KEY"),
                model=os.getenv("OPENAI_MODEL", "gpt-4o-mini"),
                temperature=0.1
            )
            
            # 配置Embedding
            self.embedding = OpenAIEmbedding(
                api_key=os.getenv("OPENAI_API_KEY"),
                model=os.getenv("EMBEDDING_MODEL", "text-embedding-ada-002")
            )
            
            # 检查是否有现有索引
            if self._has_existing_index():
                await self._load_search_engines()
            
            self.initialized = True
            logger.info("✅ GraphRAG服务初始化完成")
            
        except Exception as e:
            logger.error(f"❌ GraphRAG服务初始化失败: {e}")
            raise
    
    def _has_existing_index(self) -> bool:
        """检查是否有现有索引"""
        output_dir = self.data_dir / "output"
        return output_dir.exists() and any(output_dir.iterdir())
    
    async def _load_search_engines(self):
        """加载搜索引擎"""
        try:
            output_dir = self.data_dir / "output"
            
            # 读取索引数据
            entities = read_indexer_entities(str(output_dir))
            reports = read_indexer_reports(str(output_dir))
            
            # 设置全局搜索
            global_context = GlobalCommunityContext(
                community_reports=reports,
                entities=entities,
                token_encoder=self.llm.get_token_encoder()
            )
            
            self.global_search = GlobalSearch(
                llm=self.llm,
                context_builder=global_context,
                token_encoder=self.llm.get_token_encoder(),
                max_tokens=12000
            )
            
            # 设置本地搜索
            local_context = LocalSearchMixedContext(
                community_reports=reports,
                text_units=None,  # 需要根据实际情况加载
                entities=entities,
                relationships=None,  # 需要根据实际情况加载
                covariates=None,
                entity_text_embeddings=None,
                embedding_vectorstore_key=None,
                text_embedder=self.embedding,
                token_encoder=self.llm.get_token_encoder()
            )
            
            self.local_search = LocalSearch(
                llm=self.llm,
                context_builder=local_context,
                token_encoder=self.llm.get_token_encoder(),
                response_type="multiple paragraphs"
            )
            
            logger.info("✅ 搜索引擎加载完成")
            
        except Exception as e:
            logger.error(f"❌ 搜索引擎加载失败: {e}")
            raise
    
    async def build_index(self, documents: List[str], source: str = "rss", rebuild: bool = False):
        """构建知识图谱索引"""
        try:
            logger.info(f"📚 开始构建索引，文档数量: {len(documents)}")
            
            # 准备输入目录
            input_dir = self.data_dir / "input"
            input_dir.mkdir(parents=True, exist_ok=True)
            
            # 写入文档
            for i, doc in enumerate(documents):
                doc_file = input_dir / f"{source}_{i}.txt"
                doc_file.write_text(doc, encoding='utf-8')
            
            # 运行索引构建（这里需要调用GraphRAG的索引构建命令）
            # 实际实现中需要使用subprocess调用graphrag index命令
            import subprocess
            
            cmd = [
                "python", "-m", "graphrag.index",
                "--root", str(self.data_dir),
                "--verbose"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                raise Exception(f"索引构建失败: {result.stderr}")
            
            # 重新加载搜索引擎
            await self._load_search_engines()
            
            logger.info("✅ 索引构建完成")
            return {"status": "success", "message": "索引构建完成"}
            
        except Exception as e:
            logger.error(f"❌ 索引构建失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def query(self, question: str, method: str = "global", max_tokens: int = 8000, context: Optional[Dict] = None):
        """执行查询"""
        try:
            if not self.initialized:
                raise HTTPException(status_code=503, detail="GraphRAG服务未初始化")
            
            logger.info(f"🔍 执行{method}查询: {question}")
            
            if method == "global" and self.global_search:
                result = await self.global_search.asearch(question)
                return {
                    "question": question,
                    "method": method,
                    "response": result.response,
                    "context_data": result.context_data,
                    "timestamp": datetime.now().isoformat()
                }
            
            elif method == "local" and self.local_search:
                result = await self.local_search.asearch(question)
                return {
                    "question": question,
                    "method": method,
                    "response": result.response,
                    "context_data": result.context_data,
                    "timestamp": datetime.now().isoformat()
                }
            
            else:
                raise HTTPException(status_code=400, detail=f"不支持的查询方法: {method}")
                
        except Exception as e:
            logger.error(f"❌ 查询失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

# 全局GraphRAG服务实例
graphrag_service = GraphRAGService()

# API路由
@app.on_event("startup")
async def startup_event():
    """启动事件"""
    await graphrag_service.initialize()

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    return HealthResponse(
        status="healthy" if graphrag_service.initialized else "initializing",
        timestamp=datetime.now().isoformat(),
        version="1.0.0",
        services={
            "graphrag": "running" if graphrag_service.initialized else "starting",
            "llm": "connected" if graphrag_service.llm else "disconnected",
            "embedding": "connected" if graphrag_service.embedding else "disconnected"
        }
    )

@app.post("/index")
async def build_index(request: IndexRequest, background_tasks: BackgroundTasks):
    """构建索引"""
    background_tasks.add_task(
        graphrag_service.build_index,
        request.documents,
        request.source,
        request.rebuild
    )
    return {"status": "accepted", "message": "索引构建任务已启动"}

@app.post("/query")
async def query_graphrag(request: QueryRequest):
    """查询GraphRAG"""
    return await graphrag_service.query(
        request.question,
        request.method,
        request.max_tokens,
        request.context
    )

@app.get("/status")
async def get_status():
    """获取服务状态"""
    return {
        "initialized": graphrag_service.initialized,
        "data_dir": str(graphrag_service.data_dir),
        "has_index": graphrag_service._has_existing_index(),
        "global_search_ready": graphrag_service.global_search is not None,
        "local_search_ready": graphrag_service.local_search is not None
    }

if __name__ == "__main__":
    uvicorn.run(
        "server:app",
        host="0.0.0.0",
        port=8080,
        reload=False,
        log_level="info"
    )
